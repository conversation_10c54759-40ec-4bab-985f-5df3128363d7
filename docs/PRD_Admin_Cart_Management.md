# Product Requirements Document (PRD)

## Admin Cart Management System

**Document Version:** 1.0  
**Date:** December 2024  
**Author:** Development Team  
**Status:** In Progress (partially implemented)

---

## 1. Executive Summary

### 1.1 Overview

The Admin Cart Management System enables administrators to view, manage, and modify user shopping carts directly from the admin CMS. This system provides full CRUD operations on user carts, allowing staff to assist customers, manage cart contents, and convert carts to draft orders.

### 1.2 Business Value

- **Customer Support**: Enable staff to help customers with cart issues
- **Sales Assistance**: Allow staff to modify carts and create quotes
- **Cart Recovery**: Help recover abandoned carts through admin intervention
- **Operational Efficiency**: Streamline customer service workflows

### 1.3 Success Metrics

- Reduced customer support tickets related to cart issues
- Increased cart-to-order conversion rate
- Improved customer satisfaction scores
- Faster resolution of cart-related problems

---

## 2. Product Overview

### 2.1 Product Vision

Empower administrators with comprehensive cart management capabilities to provide exceptional customer service and drive sales growth.

### 2.2 Target Users

- **Primary**: Admin users with cart management permissions
- **Secondary**: Customer service representatives
- **Tertiary**: Sales team members

### 2.3 User Stories

- As an admin, I want to view any user's cart so I can help with customer issues
- As an admin, I want to add/remove items from a user's cart so I can assist with modifications
- As an admin, I want to convert a cart to a draft order so I can create quotes for customers
- As an admin, I want to see all active carts so I can identify opportunities for follow-up

---

## 3. Technical Architecture

### 3.1 System Overview

The Admin Cart Management System integrates with the existing Zurno cart infrastructure, providing admin-level access to user cart data and operations.

### 3.2 Data Flow

```
Admin Request → Admin Controller → Admin Service → Cart Models → Database
                ↓
            Response → Admin UI
```

### 3.3 Cart Structure

```
User (1) → (1) Cart → (N) CartSection → (N) CartItem
```

**Components:**

- **Cart**: Container for user's shopping session
- **CartSection**: Logical grouping of items (products, bundles)
- **CartItem**: Individual product variant with quantity and pricing

### 3.4 Database Schema

**Existing Tables (Current Implementation):**

```sql
-- zn_carts table
CREATE TABLE zn_carts (
  id UUID PRIMARY KEY,
  userId UUID REFERENCES zn_users(id) ON DELETE SET NULL NULLABLE,
  createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deletedAt TIMESTAMP WITH TIME ZONE NULLABLE
);

-- zn_cart_sections table
CREATE TABLE zn_cart_sections (
  id UUID PRIMARY KEY,
  cartId UUID REFERENCES zn_carts(id) ON DELETE CASCADE NOT NULL,
  bundleId UUID REFERENCES zn_bundle_products(id) ON DELETE SET NULL NULLABLE,
  title VARCHAR NULLABLE,
  type VARCHAR DEFAULT 'product' NOT NULL,
  quantity INTEGER DEFAULT 1 NOT NULL,
  rawTotal DECIMAL(10,2) NULLABLE,
  total DECIMAL(10,2) DEFAULT 0 NOT NULL,
  rawPrice DECIMAL(10,2) NULLABLE,
  price DECIMAL(10,2) DEFAULT 0 NOT NULL,
  createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deletedAt TIMESTAMP WITH TIME ZONE NULLABLE
);

-- zn_cart_items table
CREATE TABLE zn_cart_items (
  id UUID PRIMARY KEY,
  cartSectionId UUID REFERENCES zn_cart_sections(id) ON DELETE CASCADE NOT NULL,
  productId UUID REFERENCES zn_products(id) ON DELETE CASCADE NOT NULL,
  variantId UUID REFERENCES zn_product_variants(id) ON DELETE CASCADE NOT NULL,
  productName VARCHAR NOT NULL,
  variantName VARCHAR NULLABLE,
  image VARCHAR NULLABLE,
  sku VARCHAR NULLABLE,
  quantity INTEGER DEFAULT 1 NOT NULL,
  rawPrice DECIMAL(10,2) NULLABLE,
  price DECIMAL(10,2) DEFAULT 0 NOT NULL,
  bundleDiscountId VARCHAR NULLABLE COMMENT 'zn_bundle_product_discounts id',
  createdAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  deletedAt TIMESTAMP WITH TIME ZONE NULLABLE
);
```

**Model Relationships:**

- `ZnCart` (app/models/zn_cart.ts) → hasMany → `ZnCartSection`
- `ZnCartSection` (app/models/zn_cart_section.ts) → hasMany → `ZnCartItem`
- `ZnCartItem` (app/models/zn_cart_item.ts) → belongsTo → `ZnProduct`, `ZnProductVariant`, `ZnBundleProductDiscount`

---

## 4. Functional Requirements

### 4.1 Core Cart Management (Current State)

#### 4.1.1 View User Cart

- **Endpoint**: `GET /v1/admin/carts/user/:userId`
- **Description**: Retrieve the cart for a specific user. If no active cart exists, a new cart is created.
- **Authentication**: JWT admin guard (`jwt_admin`)
- **Authorization**: Not enforced via Bouncer yet (planned)
- **Controller**: `AdminCartController.getUserCart()`
- **Response**: Complete cart data with sections and items, plus computed `itemCount` and `totalValue`

```json
{
  "id": "uuid",
  "userId": "uuid",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  },
  "cartSections": [
    {
      "id": "uuid",
      "type": "product",
      "title": "Section Title",
      "quantity": 1,
      "total": 29.99,
      "cartItems": [
        {
          "id": "uuid",
          "productName": "Product Name",
          "variantName": "Variant Name",
          "quantity": 1,
          "price": 29.99,
          "image": "image_url",
          "sku": "SKU123"
        }
      ]
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "itemCount": 2,
  "totalValue": 59.98
}
```

#### 4.1.2 List All Carts

- **Endpoint**: `GET /v1/admin/carts`
- **Description**: List all user carts with filtering and pagination
- **Authentication**: JWT admin guard (`jwt_admin`)
- **Authorization**: Not enforced via Bouncer yet (planned)
- **Controller**: `AdminCartController.index()`
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10, max: 100)
  - `search`: Search by user email/name
  - `status`: Filter by cart status ('active', 'abandoned', 'converted')
  - `userId`: Filter by specific user ID
  - `dateFrom`: Filter carts created after date
  - `dateTo`: Filter carts created before date
- **Validation**: `cartFilterValidator`
- **Response**: Paginated list of carts with user information

```json
{
  "data": [
    {
      "id": "uuid",
      "userId": "uuid",
      "user": {
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe"
      },
      "itemCount": 3,
      "totalValue": 89.97,
      "lastActivity": "2024-01-01T00:00:00Z",
      "status": "active"
    }
  ],
  "meta": {
    "total": 150,
    "perPage": 10,
    "currentPage": 1,
    "lastPage": 15,
    "firstPage": 1,
    "firstPageUrl": "/?page=1",
    "lastPageUrl": "/?page=15",
    "nextPageUrl": "/?page=2",
    "previousPageUrl": null
  }
}
```

#### 4.1.3 Create Cart for User

- Status: Planned (not implemented)
- Proposed endpoint: `POST /v1/admin/carts`
- Will require Bouncer authorization: `ACTION.CREATE` on `RESOURCE.CART`
- **Request Body**:

```json
{
  "userId": "uuid"
}
```

- **Validation**: `createCartValidator`
- **Business Rules**:
  - User must exist and be active
  - User cannot already have an active cart
  - Admin action is logged for audit trail
- **Response**: Created cart object

```json
{
  "id": "uuid",
  "userId": "uuid",
  "cartSections": [],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### 4.1.4 Delete Cart

- **Endpoint**: `DELETE /v1/admin/carts/:id`
- **Description**: Hard delete a user's cart and all related sections and items (payment-flow compliant)
- **Authentication**: JWT admin guard (`jwt_admin`)
- **Authorization**: Bouncer enforced `ACTION.DELETE` on `RESOURCE.CART`
- **Controller**: `AdminCartController.destroy()`
- **Business Rules**:
  - Cart must exist and not be already deleted
  - Hard delete is performed for compliance with payment processing
  - Cascade removal done via batch deletes: first `ZnCartItem` by `cartSectionId`, then `ZnCartSection` by `id`, then the `ZnCart`
  - Batch deletes are used to avoid memory pressure in production
- **Response**: `200 OK` with `{ message: "Cart deleted successfully" }`

### 4.2 Cart Section Management

#### 4.2.1 Add Section

- **Endpoint**: `POST /admin/carts/:cartId/sections`
- **Description**: Add a new section to a cart
- **Authorization**: `await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)`
- **Controller**: `AdminCartController.addSection()`
- **Service**: `AdminCartService.addSection(cartId: string, sectionData: IAddSectionData)`
- **Request Body**:

```json
{
  "type": "product|bundle",
  "title": "Section Title",
  "quantity": 1,
  "bundleId": "uuid" // Optional, required if type is 'bundle'
}
```

- **Validation**: `addSectionValidator`
- **Business Rules**:
  - Cart must exist and not be deleted
  - If type is 'bundle', bundleId must be provided and valid
  - Bundle must be active and available
  - Section pricing is calculated based on type and bundle discounts
- **Response**: Created section object

```json
{
  "id": "uuid",
  "cartId": "uuid",
  "type": "product",
  "title": "Section Title",
  "quantity": 1,
  "total": 0,
  "price": 0,
  "bundleId": null,
  "cartItems": [],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### 4.2.2 Update Section

- **Endpoint**: `PUT /admin/carts/:cartId/sections/:sectionId`
- **Description**: Update section properties
- **Authorization**: `await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)`
- **Controller**: `AdminCartController.updateSection()`
- **Service**: `AdminCartService.updateSection(cartId: string, sectionId: string, updateData: IUpdateSectionData)`
- **Request Body**: Same as add section
- **Validation**: `updateSectionValidator`
- **Business Rules**:
  - Section must exist and belong to the specified cart
  - Cannot change section type after creation
  - Pricing is recalculated when quantity or bundle changes
  - All section items are validated for availability
- **Response**: Updated section object

#### 4.2.3 Remove Section

- **Endpoint**: `DELETE /admin/carts/:cartId/sections/:sectionId`
- **Description**: Remove a section and all its items
- **Authorization**: `await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)`
- **Controller**: `AdminCartController.removeSection()`
- **Service**: `AdminCartService.removeSection(cartId: string, sectionId: string, adminId: string)`
- **Business Rules**:
  - Section must exist and belong to the specified cart
  - All cart items in the section are cascade deleted
  - Cart totals are recalculated
  - Admin action is logged for audit trail
- **Response**: 204 No Content

### 4.3 Cart Item Management

#### 4.3.1 Add Item

- **Endpoint**: `POST /admin/carts/:cartId/sections/:sectionId/items`
- **Description**: Add a product variant to a cart section
- **Authorization**: `await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)`
- **Controller**: `AdminCartController.addItem()`
- **Service**: `AdminCartService.addItem(cartId: string, sectionId: string, itemData: IAddItemData)`
- **Request Body**:

```json
{
  "variantId": "uuid",
  "quantity": 1,
  "bundleDiscountId": "uuid" // Optional
}
```

- **Validation**: `addItemValidator`
- **Business Rules**:
  - Section must exist and belong to the specified cart
  - Variant must be available for sale
  - Product must be active
  - Product cannot be a gift item
  - Bundle discount must be valid if provided
  - Item pricing is calculated with applicable discounts
  - Section totals are recalculated
- **Response**: Created item object

```json
{
  "id": "uuid",
  "cartSectionId": "uuid",
  "productId": "uuid",
  "variantId": "uuid",
  "productName": "Product Name",
  "variantName": "Variant Name",
  "quantity": 1,
  "price": 29.99,
  "rawPrice": 29.99,
  "image": "image_url",
  "sku": "SKU123",
  "bundleDiscountId": null,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

#### 4.3.2 Update Item

- **Endpoint**: `PUT /admin/carts/:cartId/sections/:sectionId/items/:itemId`
- **Description**: Update item quantity
- **Authorization**: `await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)`
- **Controller**: `AdminCartController.updateItem()`
- **Service**: `AdminCartService.updateItem(cartId: string, sectionId: string, itemId: string, updateData: IUpdateItemData)`
- **Request Body**:

```json
{
  "quantity": 2
}
```

- **Validation**: `updateItemValidator`
- **Business Rules**:
  - Item must exist and belong to the specified section and cart
  - Quantity 0 removes the item
  - Quantity must be positive
  - Product variant availability is re-validated
  - Section and cart totals are recalculated
- **Response**: Updated item object

#### 4.3.3 Remove Item

- **Endpoint**: `DELETE /admin/carts/:cartId/sections/:sectionId/items/:itemId`
- **Description**: Remove an item from a section
- **Authorization**: `await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)`
- **Controller**: `AdminCartController.removeItem()`
- **Service**: `AdminCartService.removeItem(cartId: string, sectionId: string, itemId: string, adminId: string)`
- **Business Rules**:
  - Item must exist and belong to the specified section and cart
  - Section totals are recalculated after removal
  - If section becomes empty, it may be automatically removed
  - Admin action is logged for audit trail
- **Response**: 204 No Content

### 4.4 Advanced Features

#### 4.4.1 Convert to Draft Order

- **Endpoint**: `POST /admin/carts/:cartId/convert-to-draft-order`
- **Description**: Convert a cart to a draft order for quote creation
- **Authorization**: `await bouncer.authorize('allow', 'convert', RESOURCE.CART)`
- **Controller**: `AdminCartController.convertToDraftOrder()`
- **Service**: `AdminCartService.convertCartToDraftOrder(cartId: string, adminId: string)`
- **Request Body**:

```json
{
  "note": "Admin conversion note",
  "shippingAddressId": "uuid" // Optional, uses user's default if not provided
}
```

- **Business Rules**:
  - Cart must belong to a user (not guest)
  - Cart must have items and all items must be available for sale
  - User must have a valid shipping address
  - Cart is soft deleted after successful conversion
  - Integrates with existing DraftOrderService
  - Admin action is logged for audit trail
- **Response**: Created draft order object

```json
{
  "id": "uuid",
  "code": 1001,
  "userId": "uuid",
  "status": "draft",
  "totalPrice": 89.97,
  "subTotalPrice": 79.97,
  "totalTax": 8.0,
  "totalShipping": 2.0,
  "totalDiscount": 0,
  "note": "Converted from cart by admin",
  "details": [
    {
      "id": "uuid",
      "title": "Product Name",
      "variantTitle": "Variant Name",
      "quantity": 1,
      "price": 29.99,
      "amount": 29.99
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z"
}
```

#### 4.4.2 Cart Analytics

- **Endpoint**: `GET /admin/carts/analytics`
- **Description**: Get cart performance metrics and dashboard data
- **Authorization**: `await bouncer.authorize('allow', 'analytics', RESOURCE.CART)`
- **Controller**: `AdminCartController.getAnalytics()`
- **Service**: `AdminCartService.getCartAnalytics(params: IAnalyticsParams)`
- **Query Parameters**:
  - `period`: Time period ('7d', '30d', '90d', '1y')
  - `dateFrom`: Custom start date
  - `dateTo`: Custom end date
- **Response**: Analytics data including:

```json
{
  "summary": {
    "totalActiveCarts": 150,
    "abandonedCartCount": 45,
    "convertedCartCount": 25,
    "averageCartValue": 67.89,
    "conversionRate": 16.67
  },
  "trends": {
    "cartCreation": [
      { "date": "2024-01-01", "count": 12 },
      { "date": "2024-01-02", "count": 15 }
    ],
    "cartConversion": [
      { "date": "2024-01-01", "count": 3 },
      { "date": "2024-01-02", "count": 4 }
    ]
  },
  "topProducts": [
    {
      "productId": "uuid",
      "productName": "Popular Product",
      "cartCount": 25,
      "totalQuantity": 45
    }
  ],
  "userSegments": {
    "newUsers": 30,
    "returningUsers": 120,
    "averageItemsPerCart": 2.3
  }
}
```

---

## 5. Non-Functional Requirements

### 5.1 Performance

- **Response Time**: API endpoints must respond within 500ms
- **Throughput**: Support 100+ concurrent admin users
- **Pagination**: Efficient pagination for large cart lists

### 5.2 Security

- **Authentication**: All endpoints require admin JWT token
- **Authorization**: Role-based access control for cart operations
- **Audit Logging**: Log all cart modifications with admin user context
- **Data Privacy**: No exposure of sensitive customer information

### 5.3 Scalability

- **Database**: Efficient queries with proper indexing
- **Caching**: Redis caching for frequently accessed cart data
- **Horizontal Scaling**: Support for multiple admin instances

### 5.4 Reliability

- **Error Handling**: Comprehensive error messages and status codes
- **Validation**: Input validation for all cart operations
- **Transaction Safety**: Database transactions for multi-step operations

---

## 6. User Interface Requirements

### 6.1 Admin Dashboard

- **Cart Overview**: Summary of all active carts
- **User Search**: Search users by email or name
- **Cart Status**: Visual indicators for cart states

### 6.2 Cart Management Interface

- **Cart Viewer**: Hierarchical display of cart → sections → items
- **Item Editor**: Inline editing of quantities and removal
- **Section Manager**: Add/remove/modify cart sections
- **Bulk Operations**: Select multiple items for batch operations

### 6.3 User Cart Interface

- **User Lookup**: Search and select user
- **Cart Display**: Full cart contents with pricing
- **Action Buttons**: Convert to order, clear cart, etc.

---

## 7. Integration Requirements

### 7.1 Existing Systems

- **Zurno Cart System**: Core cart data and operations
- **User Management**: User authentication and permissions
- **Product Catalog**: Product and variant information
- **Draft Order System**: Order creation workflow

### 7.2 External Dependencies

- **Shopify Integration**: Product availability and pricing
- **Payment Processing**: Cart value calculations
- **Inventory System**: Stock level validation

---

## 8. Implementation Plan

### 8.1 Phase 1: Core Infrastructure (Week 1-2)

**8.1.1 Authorization Setup**

- [ ] Add CART resource to authorization constants (`app/constants/authorization.ts`)
- [ ] Create cart permissions using AuthorizationService (`database/permissions/`)
- [ ] Update admin role permissions to include cart management

**8.1.2 Admin Cart Controller**

- [ ] Create `admin/controllers/cart/admin_cart_controller.ts`
- [ ] Implement base controller structure following existing admin patterns
- [ ] Add proper authorization using bouncer middleware
- [ ] Include comprehensive JSDoc documentation for Swagger

**8.1.3 Admin Cart Service**

- [ ] Create `admin/services/cart/admin_cart_service.ts`
- [ ] Implement cart business logic and data access
- [ ] Add cart analytics and reporting methods
- [ ] Integrate with existing ShopCartService for cart operations

**8.1.4 Admin Cart Routes**

- [ ] Create `admin/route/v1/cart/cart_router.ts`
- [ ] Set up RESTful routes with proper middleware
- [ ] Add to main admin router (`admin/route/v1/index.ts`)
- [ ] Configure JWT admin authentication

**8.1.5 Validators and Types**

- [ ] Create `admin/validators/cart/cart_validator.ts`
- [ ] Define comprehensive validation rules using Vine
- [ ] Create TypeScript interfaces for cart operations
- [ ] Add proper error handling and validation messages

### 8.2 Phase 2: Basic Operations (Week 3-4)

**8.2.1 Cart Viewing Operations**

- [ ] Implement `GET /admin/carts` - List all carts with pagination
- [ ] Implement `GET /admin/carts/user/:userId` - Get user's cart
- [ ] Implement `GET /admin/carts/:cartId` - Get specific cart
- [ ] Add filtering, searching, and sorting capabilities
- [ ] Include cart sections and items with proper preloading

**8.2.2 Cart Creation and Deletion**

- [ ] Implement `POST /admin/carts` - Create cart for user
- [ ] Implement `DELETE /admin/carts/:cartId` - Soft delete cart
- [ ] Add validation for user existence and cart constraints
- [ ] Implement proper error handling and responses

**8.2.3 Section Management**

- [ ] Implement `POST /admin/carts/:cartId/sections` - Add section
- [ ] Implement `PUT /admin/carts/:cartId/sections/:sectionId` - Update section
- [ ] Implement `DELETE /admin/carts/:cartId/sections/:sectionId` - Remove section
- [ ] Add bundle product validation and pricing calculations

**8.2.4 Item Management**

- [ ] Implement `POST /admin/carts/:cartId/sections/:sectionId/items` - Add item
- [ ] Implement `PUT /admin/carts/:cartId/sections/:sectionId/items/:itemId` - Update item
- [ ] Implement `DELETE /admin/carts/:cartId/sections/:sectionId/items/:itemId` - Remove item
- [ ] Add product variant validation and availability checks

### 8.3 Phase 3: Advanced Features (Week 5-6)

**8.3.1 Cart-to-Draft-Order Conversion**

- [ ] Implement `POST /admin/carts/:cartId/convert-to-draft-order`
- [ ] Integrate with existing DraftOrderService
- [ ] Add proper cart clearing after conversion
- [ ] Handle address and shipping calculations

**8.3.2 Cart Analytics**

- [ ] Implement `GET /admin/carts/analytics` - Cart performance metrics
- [ ] Add cart abandonment tracking
- [ ] Calculate conversion rates and average cart values
- [ ] Create reporting dashboard data

**8.3.3 Bulk Operations**

- [ ] Implement bulk cart deletion
- [ ] Add bulk section/item operations
- [ ] Create batch cart processing capabilities
- [ ] Add progress tracking for bulk operations

**8.3.4 Audit Logging**

- [ ] Integrate with existing audit system
- [ ] Log all cart modifications with admin context
- [ ] Add cart history tracking
- [ ] Implement audit trail reporting

### 8.4 Phase 4: Testing & Deployment (Week 7-8)

**8.4.1 Unit and Integration Testing**

- [ ] Create comprehensive test suite in `tests/functional/admin/cart/`
- [ ] Test all API endpoints with various scenarios
- [ ] Add validation testing and error handling tests
- [ ] Test authorization and permission scenarios

**8.4.2 Performance Testing**

- [ ] Load test cart listing with large datasets
- [ ] Test concurrent cart operations
- [ ] Optimize database queries and indexing
- [ ] Monitor memory usage and response times

**8.4.3 Security Review**

- [ ] Audit authorization implementation
- [ ] Test for SQL injection and XSS vulnerabilities
- [ ] Validate input sanitization
- [ ] Review data privacy compliance

**8.4.4 Production Deployment**

- [ ] Create deployment scripts and migrations
- [ ] Set up monitoring and alerting
- [ ] Configure production environment variables
- [ ] Plan rollback procedures

---

## 9. Testing Requirements

### 9.1 Unit Tests

- **Service Layer**: Test all cart operations
- **Controller Layer**: Test API endpoints
- **Validation**: Test input validation rules

### 9.2 Integration Tests

- **Database Operations**: Test cart CRUD operations
- **API Endpoints**: Test complete request/response cycles
- **Permission System**: Test admin access controls

### 9.3 Performance Tests

- **Load Testing**: Test with multiple concurrent users
- **Database Performance**: Test query performance with large datasets
- **Memory Usage**: Monitor memory consumption during operations

---

## 10. Deployment & Monitoring

### 10.1 Deployment

- **Environment**: Production admin environment
- **Rollback Plan**: Ability to rollback to previous version
- **Database Migrations**: Safe deployment of schema changes

### 10.2 Monitoring

- **API Metrics**: Response times, error rates, throughput
- **Database Performance**: Query execution times, connection usage
- **Error Tracking**: Monitor and alert on system errors
- **User Activity**: Track admin cart management usage

---

## 11. Success Criteria

### 11.1 Technical Success

- [ ] All API endpoints respond within 500ms
- [ ] Zero security vulnerabilities
- [ ] 99.9% uptime for cart management operations
- [ ] Successful integration with existing systems

### 11.2 Business Success

- [ ] 20% reduction in cart-related support tickets
- [ ] 15% increase in cart-to-order conversion rate
- [ ] 90% admin user satisfaction score
- [ ] Measurable improvement in customer service efficiency

---

## 12. Risk Assessment

### 12.1 Technical Risks

- **Performance Impact**: Cart operations may affect system performance
- **Data Consistency**: Complex cart operations may introduce data inconsistencies
- **Integration Complexity**: Shopify integration may introduce external dependencies

### 12.2 Mitigation Strategies

- **Performance**: Implement caching and optimize database queries
- **Data Consistency**: Use database transactions and comprehensive validation
- **Integration**: Implement fallback mechanisms and error handling

---

## 13. Future Enhancements

### 13.1 Phase 2 Features

- **Cart Templates**: Pre-configured cart setups for common scenarios
- **Automated Cart Recovery**: AI-powered cart abandonment recovery
- **Advanced Analytics**: Predictive cart behavior analysis

### 13.2 Integration Opportunities

- **CRM Integration**: Connect cart data with customer relationship management
- **Marketing Automation**: Trigger campaigns based on cart behavior
- **Inventory Optimization**: Use cart data for demand forecasting

---

## 14. Detailed Implementation Specifications

### 14.1 File Structure and Organization (Current Snippets)

**Admin Cart Controller** (`admin/controllers/cart/admin_cart_controller.ts`)

```typescript
import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '#constants/authorization'
import { AdminCartService } from '#adminServices/cart/admin_cart_service'
import {
  createCartValidator,
  updateCartValidator,
  addSectionValidator,
  addItemValidator,
  cartFilterValidator,
} from '#adminValidators/cart/cart_validator'

export default class AdminCartController {
  private adminCartService: AdminCartService

  constructor() {
    this.adminCartService = new AdminCartService()
  }

  /**
   * @index
   * @tag Admin Cart
   * @summary List all carts with pagination and filtering
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(10)
   * @queryParam search - Search by user email/name - @type(string)
   * @queryParam status - Filter by cart status - @type(string)
   * @responseBody 200 - {"data": [<ZnCart>], "meta": {"total": 100, "lastPage": 10}}
   * @responseBody 401 - Unauthorized access
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)
    // Implementation details...
  }

  // Additional methods following the same pattern...
}
```

**Admin Cart Service** (`admin/services/cart/admin_cart_service.ts`)

```typescript
import ZnCart from '#models/zn_cart'
import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'
import ZnUser from '#models/zn_user'
import { ShopCartService } from '#services/shop/shop_cart_service'
import { DraftOrderService } from '#services/shop/draft_order_service'

export class AdminCartService {
  private shopCartService: ShopCartService
  private draftOrderService: DraftOrderService

  constructor() {
    this.shopCartService = new ShopCartService()
    this.draftOrderService = new DraftOrderService()
  }

  async getAllCarts(params: ICartFilterParams) {
    const query = ZnCart.query()
      .preload('cartSections', (sectionQuery) => {
        sectionQuery.preload('cartItems', (itemQuery) => {
          itemQuery.preload('variant').preload('product')
        })
      })
      .whereNull('deletedAt')

    // Apply filters, pagination, search
    if (params.search) {
      query.whereHas('user', (userQuery) => {
        userQuery
          .whereILike('email', `%${params.search}%`)
          .orWhereILike('firstName', `%${params.search}%`)
          .orWhereILike('lastName', `%${params.search}%`)
      })
    }

    return query.paginate(params.page || 1, params.limit || 10)
  }

  async convertCartToDraftOrder(cartId: string, adminId: string) {
    // Implementation using existing DraftOrderService
    // Clear cart after successful conversion
    // Log admin action for audit trail
  }

  // Additional service methods...
}
```

**Admin Cart Routes** (implemented)

```typescript
import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminCartController = () => import('#adminControllers/cart/admin_cart_controller')

export default function adminCartRoutes() {
  router
    .group(() => {
      router.get('/', [AdminCartController, 'index'])
      router.get('/user/:userId', [AdminCartController, 'getUserCart'])
      router.get('/:id', [AdminCartController, 'show'])
      router.delete('/:id', [AdminCartController, 'destroy'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
    .prefix('carts')
}
```

**Cart Validators** (`admin/validators/cart/cart_validator.ts`)

```typescript
import vine from '@vinejs/vine'
import ZnUser from '#models/zn_user'
import ZnCart from '#models/zn_cart'
import ZnProductVariant from '#models/zn_product_variant'
import ZnBundleProduct from '#models/zn_bundle_product'

export const createCartValidator = vine.compile(
  vine.object({
    userId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnUser.table).where('id', field).first()
      }),
  })
)

export const addSectionValidator = vine.compile(
  vine.object({
    type: vine.enum(['product', 'bundle']),
    title: vine.string().optional(),
    quantity: vine.number().min(1),
    bundleId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnBundleProduct.table).where('id', field).first()
      })
      .optional(),
  })
)

export const addItemValidator = vine.compile(
  vine.object({
    variantId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProductVariant.table).where('id', field).first()
      }),
    quantity: vine.number().min(1),
    bundleDiscountId: vine.string().uuid().optional(),
  })
)

export const cartFilterValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    search: vine.string().optional(),
    status: vine.enum(['active', 'abandoned', 'converted']).optional(),
    userId: vine.string().uuid().optional(),
    dateFrom: vine.date().optional(),
    dateTo: vine.date().optional(),
  })
)
```

### 14.2 Authorization Implementation

**Add Cart Resource** (`app/constants/authorization.ts`)

```typescript
export enum RESOURCE {
  // ... existing resources
  CART = 'cart',
}
```

**Create Cart Permissions** (`database/permissions/*_add_cart_permissions.ts`) (implemented)

```typescript
import AuthorizationService from '#services/authorization_service'
import { ACTION, RESOURCE } from '../../app/constants/authorization.js'

export default class {
  async up() {
    await AuthorizationService.createManyPermissions(
      [ACTION.CREATE, ACTION.READ, ACTION.UPDATE, ACTION.DELETE],
      RESOURCE.CART
    )
  }

  async down() {
    await AuthorizationService.deleteManyPermissions(
      [ACTION.CREATE, ACTION.READ, ACTION.UPDATE, ACTION.DELETE],
      RESOURCE.CART
    )
  }
}
```

### 14.3 Integration with Existing Systems (Notes)

**Cart-to-Draft-Order Conversion**

```typescript
// Leverages existing DraftOrderService and ShopCartService
async convertCartToDraftOrder(cartId: string, adminId: string) {
  const cart = await ZnCart.query()
    .where('id', cartId)
    .preload('user')
    .preload('cartSections', (query) => {
      query.preload('cartItems')
    })
    .firstOrFail()

  if (!cart.user) {
    throw new Error('Cart must belong to a user for conversion')
  }

  // Convert cart sections to line items
  const lineItems = cart.cartSections.flatMap(section =>
    section.cartItems.map(item => ({
      variantId: item.variantId,
      quantity: item.quantity,
      fastBundleDiscountId: item.bundleDiscountId,
      cartSectionId: section.id
    }))
  )

  // Use existing DraftOrderService
  const draftOrder = await this.draftOrderService.createDraftOrder(cart.user, {
    items: lineItems,
    shippingAddressId: cart.user.defaultAddressId,
    note: `Converted from cart ${cart.id} by admin ${adminId}`,
    paymentMethod: 'admin_conversion'
  })

  // Clear cart after successful conversion (current approach prefers hard delete via API)
  // Recommendation: perform checkout/order workflows without relying on restoring carts

  return draftOrder
}
```

### 14.4 Testing Implementation

**Test Structure** (`tests/functional/admin/cart/`)

```
tests/functional/admin/cart/
├── admin_cart_controller.spec.ts
├── admin_cart_service.spec.ts
├── cart_authorization.spec.ts
├── cart_validation.spec.ts
└── cart_integration.spec.ts
```

**Sample Test Case** (`tests/functional/admin/cart/admin_cart_controller.spec.ts`)

```typescript
import { test } from '@japa/runner'
import { AdminCartFactory, UserFactory, CartFactory } from '#factories'

test.group('Admin Cart Controller', (group) => {
  test('should list all carts with pagination', async ({ client, assert }) => {
    const admin = await AdminCartFactory.with('roles', 1, (role) =>
      role.with('permissions', 1, (permission) => permission.merge({ key: 'read:cart' }))
    ).create()

    const response = await client
      .get('/admin/carts')
      .loginAs(admin, 'jwt_admin')
      .qs({ page: 1, limit: 10 })

    response.assertStatus(200)
    response.assertBodyContains({
      data: [],
      meta: {
        total: 0,
        lastPage: 1,
      },
    })
  })

  test('should require proper authorization', async ({ client, assert }) => {
    const admin = await AdminCartFactory.create() // No cart permissions

    const response = await client.get('/admin/carts').loginAs(admin, 'jwt_admin')

    response.assertStatus(403)
  })

  // Additional test cases...
})
```

---

**Document End**
