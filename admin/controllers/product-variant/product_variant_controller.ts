import { ACTION, RESOURCE } from '#constants/authorization'
import ZnProductVariant from '#models/zn_product_variant'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminProductVariantController {
  /**
   * @index
   * @tag Admin Product Variant
   * @summary Read all product variants
   * @paramQuery productId - ID of Product - @type(string)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnProductVariant[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read all product variants descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, filter, productId } = request.qs()

      const query = ZnProductVariant.query()
        .preload('image')
        .preload('inventories', (inventoryQuery) => {
          inventoryQuery
            .preload('warehouse')
        })
        .orderBy('sku')

      if (productId) {
        query.where('productId', productId)
      }

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.orWhereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
          queryBuilder.orWhereRaw('LOWER(sku) = LOWER(?)', [`${search}`])
        })
      }

      if (filter) {
        query.where((queryBuilder) => {
          filter.map((fil: string) =>
            queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
              `%${fil.split('=')[1]}%`,
            ])
          )
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @select
   * @tag Admin Product Variant
   * @summary Read product variant selection
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnProductVariant[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read product variant selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async select({ request, response }: HttpContext) {
    const { page = 1, search, filter, availableForSale } = request.qs()

    try {
      const query = ZnProductVariant.query()
        .whereNull('deletedAt')
        .whereHas('product', (productQuery) => {
          productQuery.where('status', 'active')
        })
        .preload('product')
        .preload('image')
        .orderBy('productId')
        .orderBy('sku')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.orWhere('sku', search)

          if (search.startsWith('+')) {
            const searchTerm = search.slice(1)
            queryBuilder
              .orWhereRaw('LOWER(title) LIKE LOWER(?)', [`%${searchTerm}%`])
              .orWhereHas('product', (productQuery) => {
                productQuery.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${searchTerm}%`])
              })
          } else {
            const searchTerms = `%${search.split(' ').filter(Boolean).join('%')}%`
            queryBuilder.orWhereILike('title', searchTerms)
            queryBuilder.orWhereHas('product', (productQuery) => {
              productQuery.whereILike('title', searchTerms)
            })
          }
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      if (availableForSale) {
        query.where('availableForSale', availableForSale === 'true')
      }

      const result = await query.paginate(page, 10)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  public async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const variantId = params.id;
      const variant = await ZnProductVariant.query()
        .where('id', variantId)
        .preload('image')
        .firstOrFail();

      return response.ok(variant);
    } catch (error) {
      return response.internalServerError(error)
    }
  }
}