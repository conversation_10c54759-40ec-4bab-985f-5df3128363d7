import ZnCart from '#models/zn_cart'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'
import { AdminCartService } from '../../services/cart/admin_cart_service.js'
import { DateTime } from 'luxon'
import {
  adminAddItemValidator,
  adminAddBundleValidator,
  adminUpdateQuantityValidator,
  adminUpdateSectionQuantityValidator,
  adminDeleteCartSectionValidator,
  cartFilterValidator,
} from '../../validators/cart/cart_validator.js'

export default class AdminCartController {
  private adminCartService: AdminCartService

  constructor() {
    this.adminCartService = new AdminCartService()
  }

  async index({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const payload = await request.validateUsing(cartFilterValidator)
      const { page = 1, limit = 10, search, userId, dateFrom, dateTo } = payload as any

      const query = ZnCart.query()
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .whereNull('deletedAt')

      if (search) {
        query.whereHas('user', (userQuery) => {
          userQuery
            .whereILike('email', `%${search}%`)
            .orWhereRaw('LOWER(`firstName`) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(`lastName`) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(CONCAT(`firstName`, " ", `lastName`)) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(CONCAT(`lastName`, " ", `firstName`)) LIKE LOWER(?)', [`%${search}%`])
            .orWhereILike('phone', `%${search}%`)
        })
      }

      if (userId) {
        query.where('userId', userId)
      }

      if (dateFrom) {
        const from = DateTime.fromJSDate(dateFrom as Date).startOf('day').toJSDate()
        query.where('updatedAt', '>=', from)
      }

      if (dateTo) {
        const to = DateTime.fromJSDate(dateTo as Date).endOf('day').toJSDate()
        query.where('updatedAt', '<=', to)
      }

      query.has('user')

      query.whereHas('cartSections', (sectionQuery) => {
        sectionQuery.whereNull('deletedAt').whereHas('cartItems', (itemQuery) => {
          itemQuery.whereNull('deletedAt')
        })
      })

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, Math.min(limit, 100))

      const transformedData = result.all().map((cart) => {
        const cartData = cart.serialize()

        const itemCount = cart.cartSections.reduce((total: number, section: any) => {
          return total + parseInt(section.quantity || '0')
        }, 0)

        const totalValue = cart.cartSections.reduce(
          (total: number, section: any) => total + parseFloat(section.total || '0'),
          0
        )

        const lastActivity = cart.updatedAt

        return {
          ...cartData,
          itemCount,
          totalValue,
          lastActivity,
        }
      })

      return response.ok({
        data: transformedData,
        meta: result.toJSON().meta,
      })
    } catch (error) {
      logger.error('Failed to get carts', { error })
      return response.badRequest({
        success: false,
        message: 'Failed to get carts',
        error: error.message,
      })
    }
  }

  async getUserCart({ params, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const { userId } = params

      let cart = await ZnCart.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .whereNull('deletedAt')
            .preload('cartItems', (itemQuery) => {
              itemQuery
                .whereNull('deletedAt')
                .preload('product')
                .preload('variant')
                .preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        cart = await ZnCart.create({ userId })
        await cart.load('user')
        await cart.load('cartSections')
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get user cart', { error, userId: params.userId })
      return response.badRequest({
        success: false,
        message: 'Failed to get user cart',
        error: error.message,
      })
    }
  }

  async show({ params, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.CART)

      const { id } = params

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found',
        })
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get cart', { error, cartId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to get cart',
        error: error.message,
      })
    }
  }

  async destroy({ params, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { id } = params
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery.whereNull('deletedAt').preload('cartItems', (itemQuery) => {
            itemQuery.whereNull('deletedAt')
          })
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found or already deleted',
        })
      }

      const cartSectionIds = cart.cartSections.map((section) => section.id)

      if (cartSectionIds.length > 0) {
        await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      }

      if (cartSectionIds.length > 0) {
        await ZnCartSection.query().whereIn('id', cartSectionIds).delete()
      }

      await cart.delete()

      logger.info('Cart deleted by admin', { adminId, cartId: id })

      return response.ok({ message: 'Cart deleted successfully' })
    } catch (error) {
      logger.error('Failed to delete cart', { error, cartId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart',
        error: error.message,
      })
    }
  }

  async addItemToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddItemValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addItemToCart({
        ...payload,
        cartId: cartId!,
      })

      await result.load('cartItems', (query: any) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      logger.info('Item added to cart by admin', { adminId, cartId, variantId: payload.variantId })

      return response.created({
        success: true,
        data: result,
        message: 'Item added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add item to cart', { error, cartId: params.cartId })
      return response.badRequest({
        success: false,
        message: 'Failed to add item to cart',
        error: error.message,
      })
    }
  }

  async addBundleToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddBundleValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addBundleToCart({
        ...payload,
        cartId: cartId!,
      })

      await result.load('cartItems', (query: any) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      logger.info('Bundle added to cart by admin', { adminId, cartId, bundleId: payload.bundleId })

      return response.created({
        success: true,
        data: result,
        message: 'Bundle added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add bundle to cart', { error, cartId: params.cartId })
      return response.badRequest({
        success: false,
        message: 'Failed to add bundle to cart',
        error: error.message,
      })
    }
  }

  async updateSectionQuantity({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminUpdateQuantityValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      
      const result = await this.adminCartService.updateSectionQuantityAdmin({
        cartSectionId,
        quantity: payload.quantity,
        notes: payload.notes,
      })

      logger.info('Section quantity updated by admin (app-side logic)', {
        adminId,
        cartSectionId,
        quantity: payload.quantity,
      })

      return response.ok({
        success: true,
        data: result,
        message: result ? 'Section quantity updated successfully' : 'Section removed successfully',
      })
    } catch (error) {
      logger.error('Failed to update section quantity', {
        error,
        cartSectionId: params.cartSectionId,
      })

      if (error.message === 'Cart section not found') {
        return response.notFound({ message: 'Cart section not found' })
      }

      return response.badRequest({
        success: false,
        message: 'Failed to update section quantity',
        error: error.message,
      })
    }
  }

  async updateSectionQuantityAdmin({ request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const payload = await request.validateUsing(adminUpdateSectionQuantityValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateSectionQuantityAdmin(payload)

      logger.info('Section quantity updated by admin (app-side logic)', {
        adminId,
        cartSectionId: payload.cartSectionId,
        quantity: payload.quantity,
      })

      return response.ok(result)
    } catch (error) {
      logger.error('Failed to update section quantity (app-side logic)', {
        error,
      })

      if (error.message === 'Cart section not found') {
        return response.notFound({ message: 'Cart section not found' })
      }

      return response.badRequest({
        success: false,
        message: 'Failed to update section quantity',
        error: error.message,
      })
    }
  }

  

  async deleteCartSection({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminDeleteCartSectionValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      await this.adminCartService.deleteCartSection(cartSectionId, payload.notes)

      logger.info('Cart section deleted by admin', { adminId, cartSectionId, notes: payload.notes })

      return response.ok({
        success: true,
        message: 'Cart section deleted successfully',
      })
    } catch (error) {
      logger.error('Failed to delete cart section', { error, cartSectionId: params.cartSectionId })
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart section',
        error: error.message,
      })
    }
  }
}
