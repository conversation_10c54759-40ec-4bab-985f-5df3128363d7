import Notification from '#models/notification'
import ZnAdmin from '#models/zn_admin'
import ZnPostComment from '#models/zn_post_comment'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { NotificationService } from '#services/notification_service'
import ZnPost from "#models/zn_post";

export default class AdminNotificationsController {
  private notificationService = new NotificationService()

  /**
   * @index
   * @tag Admin Notification
   * @summary Read all admin notifications
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @responseBody 200 - <ZnOrder[]>.append("id":"").paginated() - Read all admin notifications descriptively
   */
  /**
   * Display a list of resource
   */
  async index({ auth, request, response }: HttpContext) {
    const { page = 1 } = request.qs()

    try {
      const admin = auth.getUserOrFail() as ZnAdmin
      const query = Notification.query()
        .where({ adminId: admin.id })
        // .whereNotNull("adminId")
        .select(db.raw(`*, SUM(isRead) OVER() AS readCount`))
        .orderBy('createdAt', 'desc')

      const result = await query.paginate(page, 5)
      const meta = result.serialize().meta
      const data = result.serialize().data

      const newMeta = {
        ...meta,
        readCount: data[0]?.readCount || 0,
      }

      const newData = []
      for (const datum of data) {
        const comment = await ZnPostComment.find(datum.resourceId)
        const post = await ZnPost.find(comment?.postId || "")
        const postSource = post?.source ?? null
        newData.push({ ...datum, comment, postSource })
      }

      return response.ok({ meta: newMeta, data: newData })
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @read
   * @tag Admin Notification
   * @summary Change an admin notification to read
   * @description Change an admin notification to read descriptively
   * @paramPath id - ID of Notification - @type(string) @required
   * @responseBody 200 - <Notification> - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Notification not found"} - Not Found
   */
  /**
   * Display a list of resource
   */
  async read({ auth, params, response }: HttpContext) {
    try {
      const notificationId = params.id

      const notification = await Notification.find(notificationId)

      if (!notification) {
        return response.notFound({ message: 'Notification not found' })
      }

      const admin = auth.getUserOrFail() as ZnAdmin

      if (notification.adminId != admin.id) {
        return response.forbidden({ message: "Cannot affect others' notification" })
      }

      await this.notificationService.markAsRead(notification)

      return response.ok(notification)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @destroy
   * @tag Admin Notification
   * @summary Soft-delete an admin notification
   * @description Soft-delete an admin notification descriptively
   * @paramPath id - ID of Notification - @type(string) @required
   * @responseBody 200 - {"message":"Notification soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Notification not found"} - Not Found
   */
  /**
   * Display a list of resource
   */
  async destroy({ auth, params, response }: HttpContext) {
    try {
      const notificationId = params.id

      const notification = await Notification.find(notificationId)

      if (!notification) {
        return response.notFound({ message: 'Notification not found' })
      }

      const admin = auth.getUserOrFail() as ZnAdmin

      if (notification.adminId != admin.id) {
        return response.forbidden({ message: "Cannot affect others' notification" })
      }

      await notification.softDelete()

      return response.ok({ message: 'Notification soft-deleted successfully' })
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }
}
