import ZnInventory from '#models/zn_inventory'
import ZnWarehouse from '#models/zn_warehouse'
import type { HttpContext } from '@adonisjs/core/http'
import { createWarehouseValidator } from '../../validators/warehouse/warehouse_validator.js'

export default class WarehousesController {
  /**
   * @index
   * @tag Admin Warehouse
   * @summary Read all warehouses
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search terrm - @type(string)
   * @responseBody 200 - <ZnWarehouse[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all warehouses descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ request, response }: HttpContext) {
    const { page = 1, limit = 10, search } = request.qs()

    try {
      const query = ZnWarehouse.query().withCount('inventories')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @select
   * @tag Admin Warehouse
   * @summary Read all warehouses selections
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 5) - @type(number)
   * @paramQuery search - Search terrm - @type(string)
   * @responseBody 200 - <ZnWarehouse[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all warehouses descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async select({ request, response }: HttpContext) {
    const {
      page = 1,
      limit = 5,
      search,
      filter
    } = request.qs()

    try {
      const query = ZnWarehouse.query()
        .orderBy('name', 'asc')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * Display form to create a new record
   */
  async create({ }: HttpContext) { }

  /**
   * @store
   * @tag Admin Warehouse
   * @summary Create action
   * @requestBody <ZnWarehouse>
   * @responseBody 201 - <ZnWarehouse>.append("id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The warehouseName field must be defined","rule":"required","field":"warehouseName"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ request, response }: HttpContext) {
    const data = request.all()

    const payload = await createWarehouseValidator.validate(data)

    try {
      const created = await ZnWarehouse.create({
        name: payload.name,
        fulfilWarehouseId: payload.fulfilWarehouseId,
        code: payload.code,
        isNotification: payload.isNotification,
      })

      return response.created(created)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin Warehouse
   * @summary Read a warehouse
   * @responseBody 200 - <ZnWarehouse>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a warehouse descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const warehouseId = params.id

    try {
      const warehouse = await ZnWarehouse.query().where('id', warehouseId).first()

      if (!warehouse) {
        return response.notFound({ message: 'Warehouse not found.' })
      }

      return response.ok(warehouse)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * Edit individual record
   */
  async edit({ params, response }: HttpContext) {
    const warehouseId = params.id

    try {
      const warehouse = await ZnWarehouse.query().where('id', warehouseId).first()

      if (!warehouse) {
        return response.notFound({ message: 'Warehouse not found.' })
      }

      return response.ok({
        data: warehouse.serialize(),
      })
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const data = request.all()

    const warehouseId = params.id

    const warehouse = await ZnWarehouse.find(warehouseId)

    if (!warehouse) {
      return response.notFound({ message: 'Warehouse not found.' })
    }

    const payload = await createWarehouseValidator.validate(data)

    try {
      warehouse.name = payload.name || warehouse.name
      warehouse.code = payload.code || warehouse.code
      warehouse.isNotification = payload.isNotification || warehouse.isNotification

      if (payload.isVendorPrimary === true && warehouse.shopifyLocationId) {
        const vendorPrimaryWarehouse = await ZnWarehouse.query()
          .whereNotNull('shopifyLocationId')
          .whereNot({ id: warehouse.id })
          .where({ isVendorPrimary: true })
          .first()

        if (vendorPrimaryWarehouse) {
          await vendorPrimaryWarehouse.merge({ isVendorPrimary: false }).save()
        }

        warehouse.isVendorPrimary = true
      }

      if (payload.isPrimary === true && warehouse.shopifyLocationId) {
        const vendorPrimaryWarehouse = await ZnWarehouse.query()
          .whereNotNull('shopifyLocationId')
          .whereNot({ id: warehouse.id })
          .where({ isPrimary: true })
          .first()

        if (vendorPrimaryWarehouse) {
          await vendorPrimaryWarehouse.merge({ isPrimary: false }).save()
        }

        warehouse.isPrimary = true
      }

      const updated = await warehouse.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @destroy
   * @tag Admin Warehouse
   * @summary Soft-delete a warehouse
   * @description Soft-delete a warehouse descriptively
   * @paramPath id - ID of Warehouse - @type(string) @required
   * @responseBody 200 - {"message":"Warehouse soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Warehouse not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ params, response }: HttpContext) {
    const warehouseId = params.id

    try {
      const warehouse = await ZnWarehouse.find(warehouseId)

      if (!warehouse) {
        return response.notFound({ message: 'Warehouse not found' })
      }

      await warehouse.softDelete()

      return response.ok({ message: 'Warehouse soft-deleted successfully' })
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @listInventory
   * @tag Admin Warehouse
   * @summary Read all inventories of a warehouse
   * @paramQuery warehouseId - ID of Warehouse - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnInventory[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all inventories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async listInventory({ params, request, response }: HttpContext) {
    const warehouseId = params.id

    const warehouse = await ZnWarehouse.find(warehouseId)

    if (!warehouse) {
      return response.notFound({ message: 'Warehouse not found.' })
    }

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnInventory.query()
        .preload('variant', (variantQuery) => {
          variantQuery.preload('image')
        })
        .preload('location')
        .whereHas('location', (locationQuery) => {
          locationQuery.where('warehouseId', warehouseId)
        })

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .where('variantSku', search)
            .orWhereHas('variant', (variantQuery) => {
              variantQuery.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
            })
            .orWhereHas('location', (locationQuery) => {
              locationQuery.where('name', search)
            })
        })
      }

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
