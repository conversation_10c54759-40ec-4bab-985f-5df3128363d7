import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import ZnAffiliateTier from '#models/zn_affiliate_tier'
import { affiliationTierValidator } from '../../validators/affiliation/affiliation_tier_validator.js'
import { AffiliateTierService } from '#services/affiliation/affiliation_tier_service'
import logger from '@adonisjs/core/services/logger'
import ZnProduct from '#models/zn_product'

export default class AdminAffiliationTierController {
  private tierService: AffiliateTierService;

  constructor() {
    this.tierService = new AffiliateTierService();
  }

  /**
   * @index
   * @tag Admin Affiliation Tiers
   * @summary Read all affiliation tiers
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

    const { page = 1, limit = 10 } = request.qs()

    const query = ZnAffiliateTier.query()

    const tiers = await query.orderBy('tier', 'asc').paginate(page, limit)
    return response.ok(tiers)
  }

  async store({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.AFFILIATION)

      const payload = await request.validateUsing(affiliationTierValidator)

      const existingTier = await ZnAffiliateTier.query()
        .where('tier', payload.tier)
        .first();

      if (existingTier) {
        throw new Error(`Another tier with tier number ${payload.tier} was already existed`);
      }

      const tier = await this.tierService.createTier(
        payload.tier,
        payload.title,
        payload.requiredNewCustomers ?? 0,
        payload.defaultCommission,
        payload.defaultDiscount,
      );

      return response.created(tier)
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const tierId = params.id;
      if (!tierId) {
        throw new Error('Tier ID is required');
      }

      return await ZnAffiliateTier.findOrFail(tierId)

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async showNextTier({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const currentTier = await ZnAffiliateTier.findOrFail(params.id);

      const nextTier = await ZnAffiliateTier.query()
        .where('tier', '>', currentTier.tier)
        .orderBy('tier', 'asc')
        .first();

      return response.ok(nextTier)

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async showDiscountedProducts({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const tierId = params.id
      const { searchTerm, page = 1, limit = 10 } = request.qs();

      const tier = await ZnAffiliateTier
        .query()
        .where('id', tierId)
        .firstOrFail()

      await tier.load('discountCollection')

      const productQuery = tier.discountCollection
        .related('products')
        .query()
        .preload('variants');

      if (searchTerm !== null && searchTerm !== undefined && searchTerm.length > 0) {
        productQuery
          .where('title', 'LIKE', `%${searchTerm}%`)
          .orWhere('description', 'LIKE', `%${searchTerm}%`)
      }

      return await productQuery.paginate(page, limit)

    } catch (error) {
      logger.error(error);
      return response.badRequest(error);
    }
  }

  async getFullPricedProducts({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const tierId = params.id
      const { collectionId, searchTerm, page = 1, limit = 10 } = request.qs();

      const tier = await ZnAffiliateTier
        .query()
        .where('id', tierId)
        .firstOrFail()

      let appliedProductIds = []

      await tier.load('discountCollection', (query) => {
        query.preload('products')
      })
      appliedProductIds = tier.discountCollection.products.map(product => product.id)

      const query = ZnProduct.query().where('status', 'active')

      if (collectionId && collectionId.trim() !== '') {
        query.whereHas('collections', (query) => {
          query.where('zn_collections.id', collectionId)
        })
      }

      if (appliedProductIds.length > 0) {
        query.whereNotIn('id', appliedProductIds)
      }

      if (searchTerm && searchTerm.trim() !== '') {
        query.where((query) => {
          query
            .where('title', 'LIKE', `%${searchTerm}%`)
            .orWhere('description', 'LIKE', `%${searchTerm}%`)
        })
      }

      return await query.paginate(page, limit)

    } catch (error) {
      logger.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)
      const payload = await request.validateUsing(affiliationTierValidator)

      const tier = await ZnAffiliateTier.findOrFail(params.id)
      tier.merge({
        title: payload.title,
        requiredNewCustomers: payload.requiredNewCustomers,
        defaultCommission: payload.defaultCommission,
        defaultDiscount: payload.defaultDiscount,
      })
      await tier.save()

      return response.ok(tier)

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async updateDiscountCollectionByCollection({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const tierId = params.id;
      const { sourceCollectionId } = request.all();

      const tier = await this.tierService.updateDiscountCollectionByCollection(tierId, sourceCollectionId);

      return response.accepted(tier);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async updateDiscountCollectionByProductIds({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const tierId = params.id;
      const { action, productIds, collectionId } = request.all();
      if (!action) throw new Error('No action specified');
      if (!productIds || productIds.length === 0) throw new Error('No product IDs specified');

      const tier = await this.tierService.updateDiscountCollectionByProductIds(
        tierId,
        action,
        productIds,
        collectionId
      );

      return response.accepted(tier);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.AFFILIATION)

    const tier = await ZnAffiliateTier.find(params.id)
    if (!tier) {
      return response.notFound('Tier not found')
    }

    await tier.softDelete();

    return response.ok({ message: 'Tier deleted successfully' })
  }
}
