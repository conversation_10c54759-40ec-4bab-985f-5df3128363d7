import { EApprovalStatus } from "#constants/approval_status";
import { ACTION, RESOURCE } from "#constants/authorization";
import ZnAffiliateCommission from "#models/zn_affiliate_commission";
import { AffiliationCommissionService } from "#services/affiliation/affiliation_commission_service";
import { HttpContext } from "@adonisjs/core/http";
import { commissionAdjustmentValidator, commissionStatusValidator } from "../../validators/affiliation/commission_validator.js";
import parseDate from "#services/utils/parse_date";
import AffiliationNotificationService from "#services/affiliation/affiliation_notification_service";

export default class AdminAffiliateCommissionController {
  private commissionService: AffiliationCommissionService
  private affiliationNotificationService: AffiliationNotificationService

  constructor() {
    this.commissionService = new AffiliationCommissionService()
    this.affiliationNotificationService = new AffiliationNotificationService()
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const { page, limit } = request.qs();

      const commissions = await ZnAffiliateCommission.query()
        .orderBy('createdAt', 'desc')
        .preload('affiliate', (table) => {
          table.preload('user').withScopes((scopes) => scopes.withAll())
        })
        .paginate(page, limit);
      return response.ok(commissions)

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const commissionId = params.id;
      return await this.commissionService.getCommissionById(commissionId)
    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const commissionId = params.id
      const payload = await commissionAdjustmentValidator.validate(request.body())

      const commission = await ZnAffiliateCommission.findOrFail(commissionId)

      commission.adjustedAmount = payload.adjustedAmount
      commission.adjustedReason = payload.adjustedReason
      if (commission.status == EApprovalStatus.APPROVED) {
        commission.status = EApprovalStatus.AMOUNT_ADJUSTED;
      }

      await commission.save();

      return response.ok(commission)

    } catch (error) {
      console.error(error)
      if (error.status == 500)
        return response.internalServerError(error)
      else
        return response.badRequest(error)
    }
  }

  async setStatus({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const commissionId = params.id;
      const payload = await commissionStatusValidator.validate(request.body());

      const commission = await ZnAffiliateCommission.findOrFail(commissionId);
      if (commission.status === payload.status) {
        return response.notModified();
      }

      const lastStatus = commission.status;
      commission.status = payload.status;
      commission.rejectReason = payload.rejectReason ?? null;
      await commission.save();

      if (commission.status === EApprovalStatus.REJECTED) {
        await this.affiliationNotificationService.sendCommissionRejectedNotification(commission);

      } else if (commission.status === EApprovalStatus.APPROVED) {

        switch (lastStatus) {
          case EApprovalStatus.PENDING:
          case EApprovalStatus.REJECTED:
            await this.affiliationNotificationService.sendCommissionApprovedNotification(commission);
            break;
          case EApprovalStatus.ORDER_REFUNDED:
            await this.affiliationNotificationService.sendCommissionRefundedNotification(commission);
            break;
          case EApprovalStatus.AMOUNT_ADJUSTED:
            await this.affiliationNotificationService.sendCommissionAmountAdjustedNotification(commission);
            break;
        }

      }

      return response.ok(commission);

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async calculateCommissions({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const affiliateId = params.id;
      const { startDate, endDate } = request.qs();
      await this.commissionService.calculateCommissions(affiliateId, parseDate(startDate) ?? undefined, parseDate(endDate) ?? undefined);
      return response.ok({ success: true, message: 'Commissions calculated successfully.' });

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async syncCommission({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const commissionId = params.id;
      if (!commissionId) throw new Error('Commission ID is required');

      const commission = await this.commissionService.syncCommission(commissionId);
      return response.ok({
        success: true,
        commission
      });

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async syncAllCommissions({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const { affiliateId } = request.all();

      if (affiliateId) {
        const affiliate = await this.commissionService.syncCommissionsByAffiliate(affiliateId);
        return response.ok({
          success: true,
          affiliate
        });
      } else {
        await this.commissionService.syncAllCommissions();
        return response.ok({ success: true });
      }

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }
}