import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../../app/constants/authorization.js'
import { uploadReports } from '../../../../services/media/index.js'
import { InventoryReportService } from '../../../services/report/inventory_report_service.js'

export default class AdminInventoryImportReportController {
  private inventoryReportService: InventoryReportService

  constructor() {
    this.inventoryReportService = new InventoryReportService()
  }

  /**
   * @getInventoryReport
   * @tag Admin Inventory Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
  /**
   * Display a list of resource
   */
  async getInventoryReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.INVENTORY_REPORT)

    const { page = 1, limit = 20, sku } = request.qs()

    try {
      const report = await this.inventoryReportService.getPaginatedInventoryReport(
        {
          sku,
        },
        { page, limit }
      )

      return response.ok(report)
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @exportInventoryReport
   * @tag Admin Inventory Report
   * @paramQuery cutoffDate - Cutoff Date - @type(date) @example(2024-10-31)
  /**
   * Export Sale Report
   */
  async exportInventoryReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.INVENTORY_REPORT)

    const { sku } = request.qs()

    try {
      const report = await this.inventoryReportService.getAllInventoryReport({
        sku
      })

      const workbook = await this.inventoryReportService.createInventoryReportWorkbook(report)

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      return response.ok({
        filename: `Inventory Report.xlsx`,
        url: reports[0].url,
      })

    } catch (error) {
      console.log(error);
      return response.badRequest(error)
    }
  }

}
