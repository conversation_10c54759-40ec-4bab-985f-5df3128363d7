import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../../app/constants/authorization.js'
import { uploadReports } from '../../../../services/media/index.js'
import { InventoryImportReportService } from '../../../services/report/inventory_import_report_service.js'

export default class AdminInventoryImportReportController {
  private inventoryImportReportService: InventoryImportReportService

  constructor() {
    this.inventoryImportReportService = new InventoryImportReportService()
  }

  /**
   * @getInventoryReport
   * @tag Admin Inventory Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-10-31)
   * @paramQuery sku - SKU - @type(string)
  /**
   * Display a list of resource
   */
  async getInventoryReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.INVENTORY_REPORT)

    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      sku,
    } = request.qs()

    try {
      const report = await this.inventoryImportReportService.getPaginatedInventoryReport(
        {
          startDate,
          endDate,
          sku,
        },
        { page, limit }
      )

      return response.ok(report)
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @exportInventoryReport
   * @tag Admin Inventory Report
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-10-31)
   * @paramQuery sku - SKU - @type(string)
  /**
   * Export Sale Report
   */
  async exportInventoryReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.INVENTORY_REPORT)

    const {
      startDate,
      endDate,
      sku,
    } = request.qs()

    try {
      const report = await this.inventoryImportReportService.getAllInventoryReport({
        startDate,
        endDate,
        sku,
      })

      const workbook = await this.inventoryImportReportService.createInventoryReportWorkbook(report)

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      return response.ok({
        filename: `Inventory Import Report.xlsx`,
        url: reports[0].url,
      })

    } catch (error) {
      console.log(error);
      return response.badRequest(error)
    }
  }

}
