import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { ACTION, RESOURCE } from '#constants/authorization'
import { AdminShopService } from '../../services/shop/admin_shop_service.js'
import {
  adminShopSearchValidator,
  adminShopCategoryProductsValidator,
  adminShopCollectionProductsValidator,
  adminShopCategoriesValidator,
  adminShopCollectionsValidator,
  adminShopProductSuggestionsValidator,
} from '../../validators/shop/shop_validator.js'

export default class AdminShopController {
  private adminShopService: AdminShopService

  constructor() {
    this.adminShopService = new AdminShopService()
  }

  /**
   * @listCategories
   * @tag Admin Shop
   * @summary Get list of product categories for admin
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(20)
   * @responseBody 200 - List of categories with product counts
   */
  async listCategories({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const { page = 1, limit = 20 } = await request.validateUsing(adminShopCategoriesValidator)

      const categories = await this.adminShopService.getCategories(page, limit)

      return response.ok(categories)
    } catch (error) {
      logger.error('Failed to get categories for admin', { error })
      return response.badRequest({
        success: false,
        message: 'Failed to get categories',
        error: error.message,
      })
    }
  }

  /**
   * @listCategoryProducts
   * @tag Admin Shop
   * @summary Get products in a specific category for admin
   * @paramParam id - Category ID - @type(string) @required
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(20)
   * @queryParam sort - Sort field - @type(string) @default(updatedAt)
   * @queryParam order - Sort order - @type(string) @default(desc)
   * @queryParam search - Search by product name/SKU - @type(string)
   * @responseBody 200 - Paginated list of products in category
   */
  async listCategoryProducts({ params, request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const { id } = params
      const payload = await request.validateUsing(adminShopCategoryProductsValidator)

      const products = await this.adminShopService.getCategoryProducts({
        categoryId: id,
        page: payload.page || 1,
        limit: payload.limit || 20,
        sort: payload.sort || 'updatedAt',
        order: payload.order || 'desc',
        search: payload.search,
      })

      return response.ok(products)
    } catch (error) {
      logger.error('Failed to get category products for admin', { error, categoryId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to get category products',
        error: error.message,
      })
    }
  }

  /**
   * @listCollections
   * @tag Admin Shop
   * @summary Get list of collections for admin
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(20)
   * @queryParam categoryId - Filter by category - @type(string)
   * @responseBody 200 - List of collections with product counts
   */
  async listCollections({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const {
        page = 1,
        limit = 20,
        categoryId,
      } = await request.validateUsing(adminShopCollectionsValidator)

      const collections = await this.adminShopService.getCollections(page, limit, categoryId)

      return response.ok(collections)
    } catch (error) {
      logger.error('Failed to get collections for admin', { error })
      return response.badRequest({
        success: false,
        message: 'Failed to get collections',
        error: error.message,
      })
    }
  }

  /**
   * @listCollectionProducts
   * @tag Admin Shop
   * @summary Get products in a specific collection for admin
   * @paramParam id - Collection ID - @type(string) @required
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(20)
   * @queryParam orderBy - Sort field - @type(string) @default(BEST_SELLING)
   * @queryParam reverse - Sort direction - @type(boolean) @default(false)
   * @queryParam search - Search by product name/SKU - @type(string)
   * @responseBody 200 - Paginated list of products in collection
   */
  async listCollectionProducts({ params, request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const { id } = params
      const payload = await request.validateUsing(adminShopCollectionProductsValidator)

      const collection = await this.adminShopService.getCollectionProducts({
        collectionId: id,
        page: payload.page || 1,
        limit: payload.limit || 20,
        orderBy: payload.orderBy || 'BEST_SELLING',
        reverse: payload.reverse || false,
        search: payload.search,
      })

      return response.ok(collection)
    } catch (error) {
      logger.error('Failed to get collection products for admin', {
        error,
        collectionId: params.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to get collection products',
        error: error.message,
      })
    }
  }

  /**
   * @searchProducts
   * @tag Admin Shop
   * @summary Search products by name, SKU, or category for admin
   * @queryParam q - Search query - @type(string) @required
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(20)
   * @queryParam categoryId - Filter by category - @type(string)
   * @queryParam collectionId - Filter by collection - @type(string)
   * @queryParam status - Filter by status - @type(string)
   * @responseBody 200 - Paginated search results
   */
  async searchProducts({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const payload = await request.validateUsing(adminShopSearchValidator)

      const products = await this.adminShopService.searchProducts({
        query: payload.q,
        page: payload.page || 1,
        limit: payload.limit || 20,
        categoryId: payload.categoryId,
        collectionId: payload.collectionId,
        status: payload.status,
      })

      return response.ok(products)
    } catch (error) {
      logger.error('Failed to search products for admin', { error, query: request.qs().q })
      return response.badRequest({
        success: false,
        message: 'Failed to search products',
        error: error.message,
      })
    }
  }

  /**
   * @getProductDetail
   * @tag Admin Shop
   * @summary Get detailed product information for admin
   * @paramParam id - Product ID - @type(string) @required
   * @responseBody 200 - Detailed product information with variants
   */
  async getProductDetail({ params, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const { id } = params

      const product = await this.adminShopService.getProductDetail(id)

      return response.ok(product)
    } catch (error) {
      logger.error('Failed to get product detail for admin', { error, productId: params.id })
      return response.badRequest({
        success: false,
        message: 'Failed to get product detail',
        error: error.message,
      })
    }
  }

  /**
   * @getProductSuggestions
   * @tag Admin Shop
   * @summary Get product suggestions for cart management
   * @queryParam cartId - Cart ID to get suggestions for - @type(string)
   * @queryParam limit - Number of suggestions - @type(number) @default(10)
   * @responseBody 200 - List of suggested products
   */
  async getProductSuggestions({ request, response, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

      const payload = await request.validateUsing(adminShopProductSuggestionsValidator)

      const suggestions = await this.adminShopService.getProductSuggestions({
        cartId: payload.cartId,
        limit: payload.limit || 10,
      })

      return response.ok(suggestions)
    } catch (error) {
      logger.error('Failed to get product suggestions for admin', { error })
      return response.badRequest({
        success: false,
        message: 'Failed to get product suggestions',
        error: error.message,
      })
    }
  }
}
