import ZnCartSection from '#models/zn_cart_section'
import { ShopCartService } from '#services/shop/shop_cart_service'
import logger from '@adonisjs/core/services/logger'

export interface IAdminAddItemToCart {
  cartId: string
  variantId: string
  quantity: number
  affiliateId?: string | null
  notes?: string
}

export interface IAdminAddBundleToCart {
  cartId: string
  bundleId: string
  quantity: number
  discountId?: string | null
  affiliateId?: string | null
  notes?: string
  items?: Array<{
    variantId: string
    itemId: string
  }>
  collections?: Array<{
    id: string
    items: Array<{
      id: string
      variants: Array<{
        id: string
        quantity: number
      }>
    }>
  }>
}

export interface IAdminUpdateCartItem {
  cartItemId: string
  price?: number
  notes?: string
}

export interface IAdminUpdateSectionQuantity {
  cartSectionId: string
  quantity: number
  notes?: string
}

export interface IAdminUpdateSectionQuantityAdmin {
  cartSectionId: string
  quantity: number
  notes?: string
}

export class AdminCartService {
  private shopCartService: ShopCartService

  constructor() {
    this.shopCartService = new ShopCartService()
  }

  async addItemToCart(payload: IAdminAddItemToCart): Promise<ZnCartSection> {
    try {
      const result = await this.shopCartService.addItemToCart({
        cartId: payload.cartId,
        variantId: payload.variantId,
        quantity: payload.quantity,
        affiliateId: payload.affiliateId || undefined,
        userId: undefined,
      })

      if (payload.notes) {
        logger.info('Admin added item to cart', {
          cartId: payload.cartId,
          variantId: payload.variantId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      if (!result) {
        throw new Error('Failed to add item to cart')
      }
      return result
    } catch (error) {
      logger.error('Failed to add item to cart', { error, payload })
      throw error
    }
  }

  async addBundleToCart(payload: IAdminAddBundleToCart): Promise<ZnCartSection> {
    try {
      const result = await this.shopCartService.addBundleToCart({
        cartId: payload.cartId,
        bundleId: payload.bundleId,
        quantity: payload.quantity,
        discountId: payload.discountId || undefined,
        items: payload.items,
        collections: payload.collections,
        userId: undefined,
      })

      if (payload.notes) {
        logger.info('Admin added bundle to cart', {
          cartId: payload.cartId,
          bundleId: payload.bundleId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      if (!result) {
        throw new Error('Failed to add bundle to cart')
      }
      return result
    } catch (error) {
      logger.error('Failed to add bundle to cart', { error, payload })
      throw error
    }
  }

  async updateSectionQuantity(payload: IAdminUpdateSectionQuantity): Promise<ZnCartSection | null> {
    logger.warn('updateSectionQuantity is deprecated, use updateSectionQuantityAdmin instead')
    return this.updateSectionQuantityAdmin(payload)
  }

  async updateSectionQuantityAdmin(payload: IAdminUpdateSectionQuantityAdmin): Promise<ZnCartSection | null> {
    try {
      const cartSection = await ZnCartSection.query()
        .where('id', payload.cartSectionId)
        .preload('cartItems', (query) => {
          query.preload('discount').preload('variant').preload('product')
        })
        .preload('bundle', (query) => {
          query.preload('items', (query) => {
            query.preload('product').preload('variants')
          })
          query.preload('discounts')
        })
        .first()

      if (!cartSection) {
        throw new Error('Cart section not found')
      }

      // Use the exact same logic as app-side
      const result = await this.shopCartService.updateQuantityCartSection({
        cartSection,
        quantity: payload.quantity,
      })

      // Apply the same post-processing as app-side
      if (result) {
        await result.refresh()
        await result.load('bundle')
        await result.load('cartItems', (query) => {
          query.preload('discount').preload('variant').preload('product')
        })
      }

      if (payload.notes) {
        logger.info('Admin updated section quantity (app-side logic)', {
          cartSectionId: payload.cartSectionId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      return result
    } catch (error) {
      logger.error('Failed to update section quantity (app-side logic)', { error, payload })
      throw error
    }
  }

  async deleteCartSection(cartSectionId: string, notes?: string): Promise<void> {
    try {
      const cartSection = await ZnCartSection.query()
        .where('id', cartSectionId)
        .preload('cartItems')
        .firstOrFail()

      await this.shopCartService.deleteCartSection(cartSection)

      if (notes) {
        logger.info('Admin deleted cart section', {
          cartSectionId,
          notes,
        })
      }
    } catch (error) {
      logger.error('Failed to delete cart section', { error, cartSectionId })
      throw error
    }
  }
}
