import { BigQueryService } from '#services/google/big_query_service'
import env from '#start/env'
import ExcelJS from 'exceljs'
import moment from 'moment'

type InventoryImportReportParams = {
  startDate?: string
  endDate?: string,
  sku?: string,
}

export class InventoryImportReportService {
  private bigQueryService: BigQueryService

  constructor() {
    this.bigQueryService = new BigQueryService()
  }

  async getPaginatedInventoryReport(
    params: InventoryImportReportParams,
    paginations: {
      page: number
      limit: number
    }
  ) {
    const report = await this.fetchInventoryReport(params, paginations)

    const totalRows = report[0]?.totalRows || 0

    const result = {
      meta: {
        total: totalRows,
        lastPage: Math.ceil(totalRows / paginations.limit),
      },
      data: report,
    }

    return result
  }

  async getAllInventoryReport(params: InventoryImportReportParams) {
    const report = await this.fetchInventoryReport(params)

    return report
  }

  async createInventoryReportWorkbook(report: any[]) {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Inventory Import')

    worksheet.addRow([
      'Import Date',
      'SKU',
      'Name',
      'Cost Price',
      'Unit Price',
      'Quantity',
      'Measurement',
      'Supplier',
      'Warehouse',
    ])

    for (const record of report) {
      worksheet.addRow([
        record.effective_date.value,
        record.product_code,
        record.product_name,
        record.cost_price.toNumber(),
        record.unit_price.toNumber(),
        record.quantity,
        record.uom_name,
        record.supplier_name,
        record.warehouse_name,
      ])
    }

    return workbook
  }

  private async fetchInventoryReport(
    params: InventoryImportReportParams,
    paginations?: {
      page: number
      limit: number
    }
  ) {
    try {
      const startDate = params.startDate
        ? moment(params.startDate).format('yyyy-MM-DD')
        : null
      const endDate = params.endDate
        ? moment(params.endDate).format('yyyy-MM-DD')
        : null

      const startDateQuery = startDate
        ? `AND effective_date >= '${startDate}'`
        : ''

      const endDateQuery = endDate
        ? `AND effective_date <= '${endDate}'`
        : ''

      const skuQuery = params.sku ? `AND LOWER(product_code) = LOWER('${params.sku}')` : ''

      const paginationQuery = paginations
        ? `LIMIT ${paginations.limit} OFFSET ${(paginations.page - 1) * paginations.limit}`
        : ''

      const query = `
        WITH cte AS (
          SELECT *
          FROM \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.inventory_moves\`
          WHERE from_location_type IN ('supplier')
            AND state = 'done'
            ${skuQuery}
        )
        SELECT *,
          COUNT(*) OVER() AS totalRows
        FROM cte
        LEFT JOIN \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.product_suppliers\` ps ON cte.product_id = ps.product_variant_id
        WHERE 1 = 1
          ${startDateQuery}
          ${endDateQuery}
        ORDER BY effective_date DESC
        ${paginationQuery};
      `

      const data = await this.bigQueryService.runQuery(query)

      return data
    } catch (error) {
      console.log(error)
      return []
    }
  }

}
