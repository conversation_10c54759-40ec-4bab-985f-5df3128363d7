import logger from '@adonisjs/core/services/logger'
import ZnProduct from '#models/zn_product'
import ZnProductCategory from '#models/zn_product_category'
import ZnCollection from '#models/zn_collection'
import ZnStoreSetting from '#models/zn_store_setting'
import db from '@adonisjs/lucid/services/db'

export interface IAdminProductSearch {
  query: string
  page: number
  limit: number
  categoryId?: string
  collectionId?: string
  status?: string
}

export interface IAdminCategoryProducts {
  categoryId: string
  page: number
  limit: number
  sort: string
  order: string
  search?: string
}

export interface IAdminCollectionProducts {
  collectionId: string
  page: number
  limit: number
  orderBy: string
  reverse: boolean
  search?: string
}

export interface IAdminProductSuggestions {
  cartId?: string
  limit: number
}

export class AdminShopService {
  private async getOrderedCategoryIds(): Promise<string[]> {
    return (
      await ZnStoreSetting.query()
        .where('sourceName', ZnProductCategory.table)
        .select('sourceId')
        .orderBy('orderBy', 'asc')
    ).map((setting) => setting.sourceId)
  }

  private async getOrderedCollectionIds(): Promise<string[]> {
    return (
      await ZnStoreSetting.query()
        .where('sourceName', ZnCollection.table)
        .select('sourceId')
        .orderBy('orderBy', 'asc')
    ).map((setting) => setting.sourceId)
  }

  async searchProducts(payload: IAdminProductSearch) {
    try {
      let query = ZnProduct.query()
        .whereNot('status', 'draft')
        .where('isGift', false)
        .whereExists((subQuery) => {
          subQuery
            .from('zn_product_variants')
            .whereRaw('zn_product_variants.productId = zn_products.id')
        })

      query.where((subQuery) => {
        subQuery
          .where('title', 'like', `%${payload.query}%`)
          .orWhere('description', 'like', `%${payload.query}%`)
          .orWhereExists((variantSubQuery) => {
            variantSubQuery
              .from('zn_product_variants')
              .whereRaw('zn_product_variants.productId = zn_products.id')
              .where('zn_product_variants.sku', 'like', `%${payload.query}%`)
          })
      })

      if (payload.categoryId) {
        query.where('categoryId', payload.categoryId!)
      }

      if (payload.collectionId) {
        query.whereExists((collectionSubQuery) => {
          collectionSubQuery
            .from('zn_products_collections')
            .whereRaw('zn_products_collections.productId = zn_products.id')
            .where('zn_products_collections.collectionId', payload.collectionId!)
        })
      }

      if (payload.status) {
        query.where('status', payload.status!)
      }

      query
        .preload('variants', (variantQuery) => {
          variantQuery
            .select([
              'id',
              'productId',
              'title',
              'price',
              'compareAtPrice',
              'inventoryQuantity',
              'inventoryPolicy',
              'sku',
              'availableForSale',
            ])
            .orderBy('price', 'asc')
        })
        .preload('image')
        .preload('category')
        .preload('vendor')
        .preload('reviewsSummary')
        .preload('collections')

      query.orderBy('updatedAt', 'desc')

      return await query.paginate(payload.page, Math.min(payload.limit, 100))
    } catch (error) {
      logger.error('Failed to search products in admin shop service', { error, payload })
      throw error
    }
  }

  async getCategoryProducts(payload: IAdminCategoryProducts) {
    try {
      let query = ZnProduct.query()
        .where('categoryId', payload.categoryId)
        .whereNot('status', 'draft')
        .where('isGift', false)
        .whereExists((subQuery) => {
          subQuery
            .from('zn_product_variants')
            .whereRaw('zn_product_variants.productId = zn_products.id')
        })

      if (payload.search) {
        query.where((subQuery) => {
          subQuery
            .where('title', 'like', `%${payload.search}%`)
            .orWhereExists((variantSubQuery) => {
              variantSubQuery
                .from('zn_product_variants')
                .whereRaw('zn_product_variants.productId = zn_products.id')
                .where('zn_product_variants.sku', 'like', `%${payload.search}%`)
            })
        })
      }

      query.preload('variants', (variantQuery) => {
        variantQuery
          .select([
            'id',
            'productId',
            'title',
            'price',
            'compareAtPrice',
            'inventoryQuantity',
            'inventoryPolicy',
            'sku',
            'availableForSale',
          ])
          .orderBy('price', 'asc')
      })

      query
        .preload('image')
        .preload('productType')
        .preload('vendor')
        .preload('tags')
        .preload('reviewsSummary')

      query.select('*').select(
        db.raw(`
          (SELECT COUNT(*) FROM zn_product_reviews
           WHERE productId = zn_products.id
           AND status = 1) as review_count,
          (SELECT AVG(rating) FROM zn_product_reviews
           WHERE productId = zn_products.id
           AND status = 1) as average_rating
        `)
      )

      query.orderBy(payload.sort, payload.order === 'asc' ? 'asc' : 'desc')

      return await query.paginate(payload.page, Math.min(payload.limit, 100))
    } catch (error) {
      logger.error('Failed to get category products in admin shop service', { error, payload })
      throw error
    }
  }

  async getCollectionProducts(payload: IAdminCollectionProducts) {
    try {
      let query = ZnCollection.query()
        .where('id', payload.collectionId)
        .where('status', true)
        .preload('products', (productQuery) => {
          productQuery
            .whereNot('status', 'draft')
            .where('isGift', false)
            .whereExists((subQuery) => {
              subQuery
                .from('zn_product_variants')
                .whereRaw('zn_product_variants.productId = zn_products.id')
            })

          if (payload.search) {
            productQuery.where((subQuery) => {
              subQuery
                .where('title', 'like', `%${payload.search}%`)
                .orWhereExists((variantSubQuery) => {
                  variantSubQuery
                    .from('zn_product_variants')
                    .whereRaw('zn_product_variants.productId = zn_products.id')
                    .where('zn_product_variants.sku', 'like', `%${payload.search}%`)
                })
            })
          }

          if (payload.orderBy === 'BEST_SELLING') {
            productQuery.orderBy('zn_product_collections.orderBy', payload.reverse ? 'desc' : 'asc')
          } else if (payload.orderBy === 'TITLE') {
            productQuery.orderBy('zn_products.title', payload.reverse ? 'desc' : 'asc')
          } else if (payload.orderBy === 'CREATED') {
            productQuery.orderBy('zn_products.createdAt', payload.reverse ? 'desc' : 'asc')
          } else if (payload.orderBy === 'PRICE') {
            productQuery.orderByRaw(
              `(SELECT price FROM zn_product_variants WHERE zn_product_variants.productId = zn_products.id AND zn_product_variants.deletedAt IS NULL ORDER BY position ASC LIMIT 1) ${payload.reverse ? 'desc' : 'asc'}`
            )
          }
        })
        .preload('image')
        .preload('categories')

      const collection = await query.first()

      if (!collection) {
        throw new Error('Collection not found')
      }

      const allProducts = collection.products || []
      const total = allProducts.length
      const startIndex = (payload.page - 1) * payload.limit
      const endIndex = startIndex + payload.limit
      const paginatedProducts = allProducts.slice(startIndex, endIndex)

      return {
        ...collection.serialize(),
        products: paginatedProducts,
        meta: {
          total,
          perPage: payload.limit,
          currentPage: payload.page,
          lastPage: Math.ceil(total / payload.limit),
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${Math.ceil(total / payload.limit)}`,
          nextPageUrl:
            payload.page < Math.ceil(total / payload.limit) ? `?page=${payload.page + 1}` : null,
          previousPageUrl: payload.page > 1 ? `?page=${payload.page - 1}` : null,
        },
      }
    } catch (error) {
      logger.error('Failed to get collection products in admin shop service', { error, payload })
      throw error
    }
  }

  async getProductSuggestions(payload: IAdminProductSuggestions) {
    try {
      let query = ZnProduct.query()
        .whereNot('status', 'draft')
        .where('isGift', false)
        .whereExists((subQuery) => {
          subQuery
            .from('zn_product_variants')
            .whereRaw('zn_product_variants.productId = zn_products.id')
        })
        .whereExists((channelSubQuery) => {
          channelSubQuery
            .from('zn_channels')
            .whereRaw('zn_channels.productId = zn_products.id')
            .where('zn_channels.isMobile', true)
            .where('zn_channels.active', true)
        })

      if (payload.cartId) {
        query.whereNotExists((subQuery) => {
          subQuery
            .from('zn_cart_sections')
            .join('zn_cart_items', 'zn_cart_sections.id', 'zn_cart_items.cartSectionId')
            .join('zn_product_variants', 'zn_cart_items.variantId', 'zn_product_variants.id')
            .where('zn_cart_sections.cartId', payload.cartId!)
            .whereRaw('zn_product_variants.productId = zn_products.id')
        })
      }

      const popularProducts = await query
        .preload('variants', (variantQuery) => {
          variantQuery
            .select(['id', 'productId', 'title', 'price', 'compareAtPrice', 'sku'])
            .orderBy('price', 'asc')
        })
        .preload('image')
        .preload('category')
        .preload('reviewsSummary')
        .orderByRaw('RAND()')
        .limit(Math.min(payload.limit, 20))

      return popularProducts
    } catch (error) {
      logger.error('Failed to get product suggestions in admin shop service', { error, payload })
      throw error
    }
  }

  async getProductDetail(productId: string) {
    try {
      const product = await ZnProduct.query()
        .where((queryBuilder) => {
          queryBuilder
            .where('id', productId)
            .orWhere('shopifyProductId', `gid://shopify/Product/${productId}`)
        })
        .whereNot('status', 'draft')
        .where('isGift', false)
        .preload('variants', (query) => {
          query.preload('image').preload('optionValues').orderBy('position', 'asc')
        })
        .preload('reviews', (query) => {
          query.limit(5).orderBy('createdAt', 'desc')
        })
        .preload('collections')
        .preload('vendor')
        .preload('images')
        .preload('category')
        .preload('options', (query) => {
          query.preload('variantOptionValues').preload('productOptionValues')
        })
        .preload('tags')
        .preload('reviewsSummary')
        .first()

      if (!product) {
        throw new Error('Product not found')
      }

      return product
    } catch (error) {
      logger.error('Failed to get product detail in admin shop service', { error, productId })
      throw error
    }
  }

  async getCategories(page: number, limit: number) {
    try {
      const categoryIds = await this.getOrderedCategoryIds()

      const categories = await ZnProductCategory.query()
        .whereIn('id', categoryIds)
        .where('isArchived', false)
        .preload('image')
        .withCount('products', (query) => {
          query.whereNot('status', 'draft').where('isGift', false).has('variant')
        })
        .orderByRaw('FIELD(id, ?) asc', [categoryIds])
        .orderBy('name', 'asc')
        .paginate(page, Math.min(limit, 100))

      return categories
    } catch (error) {
      logger.error('Failed to get categories in admin shop service', { error })
      throw error
    }
  }

  async getCollections(page: number, limit: number, categoryId?: string) {
    try {
      const collectionIds = await this.getOrderedCollectionIds()

      let query = ZnCollection.query()
        .whereIn('id', collectionIds)
        .where('status', true)
        .preload('image')
        .preload('categories')
        .withCount('products', (productQuery) => {
          productQuery.whereNot('status', 'draft').where('isGift', false).has('variant')
        })
        .orderByRaw('FIELD(id, ?)', [collectionIds])
        .orderBy('updatedAt', 'desc')

      if (categoryId) {
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.where('id', categoryId)
        })
      }

      return await query.paginate(page, Math.min(limit, 100))
    } catch (error) {
      logger.error('Failed to get collections in admin shop service', { error })
      throw error
    }
  }
}
