import vine from '@vinejs/vine'

export const adminShopSearchValidator = vine.compile(
  vine.object({
    q: vine.string().minLength(2).maxLength(100),
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    categoryId: vine.string().uuid().optional(),
    collectionId: vine.string().uuid().optional(),
    status: vine.enum(['active', 'archived']).optional(),
  })
)

export const adminShopCategoryProductsValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    sort: vine.enum(['title', 'price', 'createdAt', 'updatedAt']).optional(),
    order: vine.enum(['asc', 'desc']).optional(),
    search: vine.string().maxLength(100).optional(),
  })
)

export const adminShopCollectionProductsValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    orderBy: vine.enum(['BEST_SELLING', 'TITLE', 'PRICE', 'CREATED']).optional(),
    reverse: vine.boolean().optional(),
    search: vine.string().maxLength(100).optional(),
  })
)

export const adminShopCategoriesValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
  })
)

export const adminShopCollectionsValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    categoryId: vine.string().uuid().optional(),
  })
)

export const adminShopProductSuggestionsValidator = vine.compile(
  vine.object({
    cartId: vine.string().uuid().optional(),
    limit: vine.number().min(1).max(50).optional(),
  })
)
