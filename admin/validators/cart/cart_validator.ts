import vine from '@vinejs/vine'
import ZnUser from '#models/zn_user'
import ZnCartSection from '#models/zn_cart_section'
import ZnProductVariant from '#models/zn_product_variant'
import ZnBundleProduct from '#models/zn_bundle_product'

export const createCartValidator = vine.compile(
  vine.object({
    userId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnUser.table).where('id', field).first()
      }),
  })
)

export const addSectionValidator = vine.compile(
  vine.object({
    type: vine.enum(['product', 'bundle']),
    title: vine.string().optional(),
    quantity: vine.number().min(1),
    bundleId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnBundleProduct.table).where('id', field).first()
      })
      .optional(),
  })
)

export const addItemValidator = vine.compile(
  vine.object({
    variantId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProductVariant.table).where('id', field).first()
      }),
    quantity: vine.number().min(1),
    bundleDiscountId: vine.string().uuid().optional(),
  })
)

export const cartFilterValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    search: vine.string().optional(),
    userId: vine.string().uuid().optional(),
    dateFrom: vine.date().optional(),
    dateTo: vine.date().optional(),
  })
)

export const adminAddItemValidator = vine.compile(
  vine.object({
    variantId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProductVariant.table).where('id', field).first()
      }),
    quantity: vine.number().min(1),
    cartId: vine.string().uuid().optional(),
    affiliateId: vine.string().uuid().optional(),
    notes: vine.string().optional(),
  })
)

export const adminAddBundleValidator = vine.compile(
  vine.object({
    bundleId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnBundleProduct.table).where('id', field).first()
      }),
    quantity: vine.number().min(1),
    cartId: vine.string().uuid().optional(),
    discountId: vine.string().uuid().optional(),
    affiliateId: vine.string().uuid().optional(),
    notes: vine.string().optional(),
    items: vine
      .array(
        vine.object({
          variantId: vine
            .string()
            .uuid()
            .exists(async (query, field) => {
              return await query.from(ZnProductVariant.table).where('id', field).first()
            }),
          itemId: vine.string(),
        })
      )
      .optional(),
    collections: vine
      .array(
        vine.object({
          id: vine.string().uuid(),
          items: vine.array(
            vine.object({
              id: vine.string(),
              variants: vine.array(
                vine.object({
                  id: vine.string().uuid(),
                  quantity: vine.number(),
                })
              ),
            })
          ),
        })
      )
      .optional(),
  })
)

export const adminUpdateQuantityValidator = vine.compile(
  vine.object({
    quantity: vine.number().min(0),
    notes: vine.string().optional(),
  })
)

export const adminUpdateSectionQuantityValidator = vine.compile(
  vine.object({
    quantity: vine.number().min(0),
    cartSectionId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnCartSection.table).where('id', field).first()
      }),
    notes: vine.string().optional(),
  })
)

export const appSideUpdateQuantityValidator = vine.compile(
  vine.object({
    quantity: vine.number(),
    cartSectionId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnCartSection.table).where('id', field).first()
      }),
  })
)

export const adminUpdateCartItemValidator = vine.compile(
  vine.object({
    price: vine.number().min(0).optional(),
    notes: vine.string().optional(),
  })
)

export const adminDeleteCartSectionValidator = vine.compile(
  vine.object({
    notes: vine.string().optional(),
  })
)

export const adminEditCartItemValidator = vine.compile(
  vine.object({
    variantId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProductVariant.table).where('id', field).first()
      })
      .optional(),
    quantity: vine.number().min(1).optional(),
    cartSectionId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnCartSection.table).where('id', field).first()
      })
      .optional(),
    affiliateId: vine.string().uuid().optional(),
    notes: vine.string().optional(),
  })
)
