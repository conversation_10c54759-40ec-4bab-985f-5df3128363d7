import { middleware } from "#start/kernel"
import router from "@adonisjs/core/services/router"

const AdminVendorOrdersController = () => import("#adminControllers/vendors/admin_vendor_orders_controller");

export default function adminVendorOrdersRoutes() {
  router
    .group(() => {
      router.get('/', [AdminVendorOrdersController, 'index']);
      router.get('/:id', [AdminVendorOrdersController, 'show']);
      router.put('/:id', [AdminVendorOrdersController, 'update']);
    })
    .prefix('orders')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }));
}
