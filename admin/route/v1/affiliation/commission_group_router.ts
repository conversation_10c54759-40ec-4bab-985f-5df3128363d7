import router from '@adonisjs/core/services/router'
import commissionRatesRoutes from './commission_rate_router.js'

const AdminAffiliationTierCommissionGroupsController = () => import('#adminControllers/affiliation/affiliation_tier_commission_groups_controller')
const AdminAffiliationCommissionRateController = () => import('#adminControllers/affiliation/affiliation_commission_rate_controller')

export default function commissionGroupsRoutes() {
  router.group(() => {
    router.put('/:id', [AdminAffiliationTierCommissionGroupsController, 'updateCommissionGroup'])
    router.delete('/:id', [AdminAffiliationTierCommissionGroupsController, 'destroyCommissionGroup'])
    router.get('/:id/products', [AdminAffiliationTierCommissionGroupsController, 'showProductsOfCommissionGroups'])
    router.put('/:id/products', [AdminAffiliationTierCommissionGroupsController, 'updateProductsOfCommissionGroups'])
    router.put('/:id/sync-with-collection', [AdminAffiliationTierCommissionGroupsController, 'syncProductsWithCollection'])

    router.get('/:id/commission-rates', [AdminAffiliationCommissionRateController, 'index'])
    router.post('/:id/commission-rates', [AdminAffiliationCommissionRateController, 'store'])

    commissionRatesRoutes()

  }).prefix('commission-groups')
}