import router from '@adonisjs/core/services/router'

const AdminAffiliationCommissionRateController = () => import('#adminControllers/affiliation/affiliation_commission_rate_controller')

export default function commissionRatesRoutes() {
  router.group(() => {
    router.get('/:id', [AdminAffiliationCommissionRateController, 'show'])
    router.put('/:id', [AdminAffiliationCommissionRateController, 'update'])
    router.delete('/:id', [AdminAffiliationCommissionRateController, 'destroy'])
  }).prefix('commission-rates')
}