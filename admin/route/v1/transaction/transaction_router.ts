import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminTransactionController = () =>
  import('#adminControllers/transaction/admin_transaction_controller')

export default function adminTransactionRoutes() {
  router
    .group(() => {
      router.get('/stats', [AdminTransactionController, 'stats'])
      router.get('/', [AdminTransactionController, 'index'])
      router.get('/:id', [AdminTransactionController, 'show'])
      router.get('/order/:orderId', [AdminTransactionController, 'getByOrder'])
      router.post('/:id/refund', [AdminTransactionController, 'processRefund'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
    .prefix('transactions')
}
