import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminShopController = () => import('#adminControllers/shop/admin_shop_controller')

export default function adminShopRoutes() {
  router
    .group(() => {
      router.get('/categories', [AdminShopController, 'listCategories'])
      router.get('/categories/:id/products', [AdminShopController, 'listCategoryProducts'])
      router.get('/collections', [AdminShopController, 'listCollections'])
      router.get('/collections/:id/products', [AdminShopController, 'listCollectionProducts'])
      router.get('/search', [AdminShopController, 'searchProducts'])
      router.get('/products/:id', [AdminShopController, 'getProductDetail'])
      router.get('/suggestions', [AdminShopController, 'getProductSuggestions'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
    .prefix('shop')
}
