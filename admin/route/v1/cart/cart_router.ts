import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminCartController = () => import('#adminControllers/cart/admin_cart_controller')

export default function adminCartRoutes() {
  router
    .group(() => {
      router.get('/', [AdminCartController, 'index'])
      router.get('/user/:userId', [AdminCartController, 'getUserCart'])
      router.get('/:id', [AdminCartController, 'show'])
      router.delete('/:id', [AdminCartController, 'destroy'])

      router.post('/:cartId/items', [AdminCartController, 'addItemToCart'])
      router.post('/:cartId/bundles', [AdminCartController, 'addBundleToCart'])
      router.put('/sections/:cartSectionId/quantity', [
        AdminCartController,
        'updateSectionQuantity',
      ])
      router.delete('/sections/:cartSectionId', [AdminCartController, 'deleteCartSection'])

      router.put('/sections/quantity', [AdminCartController, 'updateSectionQuantityAdmin'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
    .prefix('carts')
}
