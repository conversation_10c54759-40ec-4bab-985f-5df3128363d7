/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminChatController = () => import('#adminControllers/chat/chat_controller')

export default function adminChatRoutes() {
  router
    .group(() => {
      router.post('/', [AdminChatController, 'createChatRoom'])
      router.get('/', [AdminChatController, 'listChatRooms'])

      router.get('/:id/token', [Admin<PERSON>hat<PERSON>ontroller, 'createChatToken'])
      router.post('/:id/event', [AdminChatController, 'sendChatEvent'])

      router.get('/:id/messages', [AdminChatController, 'getMessages'])
      router.post('/:id/messages', [AdminChatController, 'createChatMessage'])

      router.get('messages/:id/children', [AdminChatController, 'getChildren'])
      router.delete('messages/:id', [AdminChatController, 'deleteChatMessage'])

    })
    .prefix('chat')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
