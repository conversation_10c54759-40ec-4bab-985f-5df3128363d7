import {BaseCommand} from '@adonisjs/core/ace'
import type {CommandOptions} from '@adonisjs/core/types/ace'
import queue from "@rlanz/bull-queue/services/main";
import UpdatePackageTrackingHistoryJob from "#jobs/update_package_tracking_history_job";
import ZnOrder from "#models/zn_order";
import {DateTime} from "luxon";
import {EOrderStatus} from "#constants/order";

export default class UpdateVendorOrders extends BaseCommand {
  static commandName = 'update:orders'
  static description = ''

  static options: CommandOptions = {
    startApp: true
  }

  async run() {
    const orders = await ZnOrder.query()
      .whereNotNull('id')
      .preload('fulfillments')

    for (const order of orders) {
      try {
        const fulfillments = order.fulfillments || []
        // Dispatch tracking updates for each fulfillment with tracking
        for (const fulfillment of fulfillments) {
          const { trackingNumber, trackingCompany } = fulfillment

          if (!trackingNumber || !trackingCompany) continue

          await queue.dispatch(UpdatePackageTrackingHistoryJob, {
            tracking: null,
            trackingNumber,
            trackingCompany,
            from: 'api',
          }, { queueName: 'tracking' })
        }

        // Status override: legacy fulfilled orders
        if (
          fulfillments.length === 0 &&
          order.fulfillmentStatus === 'fulfilled' &&
          order.status !== EOrderStatus.Cancelled &&
          order.createdAt < DateTime.fromISO('2024-12-31T23:59:59')
        ) {
          order.status = EOrderStatus.Completed
        }

        await order.save()

      } catch (error) {
        console.error(`Failed to update tracking for order ID ${order.id}:`, error)
      }
    }
  }
}
