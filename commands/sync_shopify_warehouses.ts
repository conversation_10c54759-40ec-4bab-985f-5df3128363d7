import ZnWarehouse from '#models/zn_warehouse';
import { ShopifyService } from '#services/shopify/shopify_service';
import { BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'shopify:sync-warehouses'
  static description = 'Sync data from Shopify warehouses'

  static options: CommandOptions = {
    startApp: true,
  }
  async run() {
    const shopifyService = new ShopifyService()

    try {
      const res = await shopifyService.getLocations()
      const locations = res.edges.map((edge: any) => edge.node)

      for (const location of locations) {
        await ZnWarehouse.updateOrCreate(
          { shopifyLocationId: location.id },
          {
            shopifyLocationId: location.id,
            name: location.name,
            isPrimary: location.isPrimary,
            isVendorPrimary: location.isPrimary,
          },
        )
      }


    } catch (error) {
      console.log(error);
    }
  }
}
