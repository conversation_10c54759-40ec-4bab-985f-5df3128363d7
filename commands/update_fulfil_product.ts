import { EApprovalStatus } from '#constants/approval_status';
import ZnProduct from '#models/zn_product';
import { FulfilService } from '#services/fulfil_service';
import { args, BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class UpdateZurnoPost extends BaseCommand {
  static commandName = 'update:fulfil-product'
  static description = 'Push product to Fulfil'


  static options: CommandOptions = {
    startApp: true
  }

  @args.string({
    required: true,
    description: "Id of Product"
  })
  declare productId: string

  async run() {
    try {
      const product = await ZnProduct.query()
        .where({ id: this.productId })
        .preload('vendor', (vendorQuery) => {
          vendorQuery.preload('warehouse')
        })
        .preload('variants')
        .first()

      if (!product) {
        return this.logger.error('Product not found')
      }

      if (!product.vendor) {
        return this.logger.error('Vendor not found')
      }

      if (product.vendor.registrationStatus != EApprovalStatus.APPROVED) {
        return this.logger.error('Vendor not approved')
      }

      if (!product.vendor.warehouse?.isVendorPrimary) {
        return this.logger.error('Vendor has no primary warehouse')
      }

      this.logger.info(`Pushing to Fulfil product: ${product.title}`);

      const fulfilService = new FulfilService()
      for (const variant of product.variants) {
        console.log();
        
        if (!variant.sku) {
          this.logger.warning(`Missing SKU in variant: ${variant.title}`)
          this.logger.warning(`Variant Id: ${variant.id}`)
          continue
        } else {
          this.logger.info(`Pushing to Fulfil variant: [${variant.sku}] ${variant.title}`);

          await fulfilService.pushProductWithBrand({
            vendor: { name: product.vendor.companyName },

            code: variant.sku,

            name: variant.title || '',
            description: product.title + ' - ' + variant.title,
            list_price: `${variant.price}`,
            cost_price: `${variant.price}`,
          })
        }
      }

    } catch (error) {
      this.error(error);
    }
  }
}
