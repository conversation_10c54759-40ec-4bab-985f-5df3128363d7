import ZnOrderDetail from "#models/zn_order_detail";
import ZnProductVariant from "#models/zn_product_variant";
import { BaseCommand, args } from "@adonisjs/core/ace";
import { CommandOptions } from "@adonisjs/core/types/ace";

export default class FixOrderVariants extends BaseCommand {
    public static commandName = 'fix:order-variants'
    public static description = 'Fix order with wrong variants'

    static options: CommandOptions = { startApp: true }

    @args.string({
        required: false,
        description: 'Id of Order. Leave empty for updating multiple orders since last week',
    })
    declare orderId: string

    public async run() {
        try {

            const query = ZnOrderDetail.query()
                .preload('order')
                .preload('variant')
                .where('createdAt', '>=', `2025-08-08 00:00:00`)

            if (this.orderId) {
                query.where({ orderId: this.orderId })
            }

            const orderDetails = await query

            for (const details of orderDetails) {
                if (!details.variant) {
                    console.log('Order', details.order?.name, details.orderId);

                    const deletedVariant = await ZnProductVariant.query()
                        .withScopes((scopes) => scopes.withAll())
                        .where({ id: details.variantId })
                        .whereNotNull('deletedAt')
                        .first()

                    if (deletedVariant) {
                        console.log("Order details", details.id);
                        console.log("Deleted variant", details.variantId);
                        console.log("Deleted variant info", deletedVariant.legacyResourceId, deletedVariant.shopifyVariantId);

                        const legacyResourceId = deletedVariant.legacyResourceId

                        const notDeletedVariant = await ZnProductVariant.query()
                            .where({ legacyResourceId })
                            .first()

                        if (notDeletedVariant) {
                            console.log('Not deleted variant', notDeletedVariant.id);
                            console.log('Not deleted variant', notDeletedVariant.legacyResourceId, notDeletedVariant.shopifyVariantId);
                            console.log();                            

                            await details.merge({ variantId: notDeletedVariant.id }).save()
                        }
                    }
                }
            }


        } catch (error) {
            console.log(error);
        }
    }
}