import {BaseCommand, args} from "@adonisjs/core/ace";
import {CommandOptions} from "@adonisjs/core/types/ace";
import queue from "@rlanz/bull-queue/services/main";
import GenerateQAPairsJob from "#jobs/generate_qa_pairs_jobs";
import {EAIAssistantRole} from "#models/zn_ai_assistant";

export default class GenerateQAPairs extends BaseCommand {
  public static commandName = 'generate:qa_pairs'
  public static description = 'Generate QA pairs for open ai training'

  static options: CommandOptions = {
    startApp: true
  }

  @args.string({
    description: 'ID of the vector store to use',
    required: true
  })
  public vectorStoreId!: string

  @args.string({
    description: 'Assistant role (enum key)',
    required: true
  })
  public assistantRole!: EAIAssistantRole

  @args.string({
    description: 'How many Q-A pairs to create',
    required: false,
    default: 50
  })
  public numOfPairs!: number


  public async run(){

    console.log('Raw parsed args', this.parsed)
    if (!this.vectorStoreId) {
      this.logger.error('Vector store ID is required. Use --vector-store-id or -v')
      return
    }

    if (!this.assistantRole) {
      this.logger.error('Assistant role is required. Use --assistant-role or -r')
      return
    }

    console.log('Vector Store Id', this.vectorStoreId)
    console.log('Assistant Role', this.assistantRole)
    console.log('Num of pairs', this.numOfPairs)
    await  queue.dispatch(
      GenerateQAPairsJob,
      {
        vectorStoreId: this.vectorStoreId,
        assistantRole: this.assistantRole,
        numOfPairs: this.numOfPairs
      },
      {queueName: 'syncData'}
    )
  }
}
