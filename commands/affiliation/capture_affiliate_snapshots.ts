import CaptureAffiliateSnapshotsJob from '#jobs/affiliation/capture_affiliate_snapshots_job'
import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from '@rlanz/bull-queue/services/main'

export default class CaptureAffiliateSnapshots extends BaseCommand {
  static commandName = 'affiliation:capture-snapshots'
  static description = 'Capture snapshots for all affiliates'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    await queue.dispatch(
      CaptureAffiliateSnapshotsJob,
      {},
      {
        queueName: 'affiliation',
      }
    )
  }
}
