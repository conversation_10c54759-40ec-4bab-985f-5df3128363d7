@layout.home({serverDomain})
  @slot('main')
    <h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
      Update: Your Influencer Tier Has Changed
    </h1>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Dear {{ userFirstName }},
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      We want to inform you that your Influencer Tier has been changed as part of our regular program review, and your account has been downgraded to a different tier.
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      <b>Here are your updated commission rate ranges:</b>
    </p>

    <div style="font-size: 16px; background-color:#fff7e7;font-weight:normal;padding:4px 24px 4px 24px">
      <ul>
        @each(range in commissionRateRanges)
          @if(range.to === '')
            <li><b>{{range.rate}}</b> on total sales above {{range.from}}</li>
          @else
            <li><b>{{range.rate}}</b> on total sales from {{range.from}} to {{range.to}}</li>
          @endif
        @end
      </ul>
    </div>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Please note these rates apply only to eligible products as outlined in our Influencer Program terms and policies. Some products may not qualify for commissions.
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      If you have any questions or need further clarification, feel free to contact us. We're here to support your continued success!
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Thank you for your continued participation in our Influencer Program. We appreciate your efforts and look forward to your ongoing partnership.
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Best regards,<br />The Zurno Team
    </p>
  @endslot
@end