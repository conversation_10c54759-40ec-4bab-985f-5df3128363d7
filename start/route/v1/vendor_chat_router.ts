/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const vendorChatController = () => import('#controllers/vendor_chat_controller')

export default function vendorChatRoutes() {
  router
    .group(() => {
      // router.get('/:id/room', [vendorChatController, 'createChatRoom'])

      router.get('/:id/token', [vendorChatController, 'createChatToken'])
      router.get('/:id/messages', [vendorChatController, 'getMessages'])
      router.post('/:id/messages', [vendorChatController, 'createChatMessage'])

      router.group(() => {
        router.get('/rooms', [vendorChatController, 'listChatRooms'])
        router.delete('messages/:id', [vendorChatController, 'deleteChatMessage'])
      }).use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))

    }).prefix('vendor-chats')
}
