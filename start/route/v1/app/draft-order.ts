import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const DraftOrderController = () => import('#controllers/app/draft_order_controller')

export default function draftOrderRoutes() {
  router
    .group(() => {
      router.post('/', [DraftOrderController, 'createDraftOrder'])
      router.get('/:id', [DraftOrderController, 'show'])
      router.put('/:id/discount-codes', [DraftOrderController, 'updateDiscountCodes'])
      router.put('/:id/address', [DraftOrderController, 'updateAddress'])
      router.put('/:id/note', [DraftOrderController, 'updateNote'])
      router.put('/:id/remove-item', [DraftOrderController, 'deleteOrderDetail'])
      router.put('/:id/handle', [DraftOrderController, 'updateHandle'])
      router.get('/:id/delivery-options', [DraftOrderController, 'getDeliveryOptions'])
    })
    .prefix('draft-orders')
    .middleware([middleware.auth()])
}
