import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const AppVendorsController = () => import('#controllers/app/app_vendors_controller')

export default function appVendorsRoutes() {
  router
    .group(() => {
      router.get('/:id', [AppVendorsController, 'show'])
      router.get('/:id/products', [AppVendorsController, 'listProducts'])

      router.group(() => {
        router.get('/:id/room', [AppVendorsController, 'getChatRoom']);
      }).use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    })
    .prefix('vendors')
}
