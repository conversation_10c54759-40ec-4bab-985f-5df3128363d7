import AuthorizationService from '#services/authorization_service'
import { ACTION, RESOURCE } from '../../app/constants/authorization.js'

export default class {
  async up() {
    await AuthorizationService.createManyPermissions(
      [ACTION.CREATE, ACTION.READ, ACTION.UPDATE, ACTION.DELETE],
      RESOURCE.CART
    )
  }

  async down() {
    await AuthorizationService.deleteManyPermissions(
      [ACTION.CREATE, ACTION.READ, ACTION.UPDATE, ACTION.DELETE],
      RESOURCE.CART
    )
  }
}
