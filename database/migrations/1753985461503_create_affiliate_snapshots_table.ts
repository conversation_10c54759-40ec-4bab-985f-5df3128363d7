import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected snapshotsTableName = 'zn_affiliate_snapshots'
  protected affiliatesTableName = 'zn_affiliates'

  async up() {
    this.schema.createTable(this.snapshotsTableName, (table) => {
      table.uuid('id').primary()

      table.string('key').notNullable()
      table.string('value').notNullable()

      table.uuid('affiliateId').references('id').inTable(this.affiliatesTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.snapshotsTableName)
  }
}