import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected commissionsTableName = 'zn_affiliate_commissions'
  protected refCodesTableName = 'zn_affiliate_ref_codes'

  async up() {
    this.schema.alterTable(this.commissionsTableName, (table) => {
      table.dropForeign('refCodeId')
    })
  }

  async down() {
    this.schema.alterTable(this.commissionsTableName, (table) => {
      table.foreign('refCodeId').references('id').inTable(this.refCodesTableName).onDelete('CASCADE')
    })
  }
}