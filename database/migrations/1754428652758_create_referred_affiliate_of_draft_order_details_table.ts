import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected draftOrderDetailsTableName = 'zn_draft_order_details'
  protected affiliatesTableName = 'zn_affiliates'

  async up() {
    this.schema.alterTable(this.draftOrderDetailsTableName, (table) => {
      table.uuid('referredAffiliateId').references('id').inTable(this.affiliatesTableName).onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.alterTable(this.draftOrderDetailsTableName, (table) => {
      table.dropForeign('referredAffiliateId')
      table.dropColumn('referredAffiliateId')
    })
  }
}