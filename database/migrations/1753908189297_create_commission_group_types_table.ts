import { ECommissionGroupType } from '#constants/commission_group_type'
import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_tier_commission_groups'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type', Object.values(ECommissionGroupType)).defaultTo(ECommissionGroupType.DEFAULT)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('type')
    })
  }
}