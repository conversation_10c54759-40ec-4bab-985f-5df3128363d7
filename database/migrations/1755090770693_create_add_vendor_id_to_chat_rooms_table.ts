import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_chat_rooms'
  protected pivotTableName = 'zn_chat_rooms_users'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('vendorId').references('id').inTable('zn_vendors').onDelete('SET NULL')
    })

    this.schema.createTable(this.pivotTableName, (table) => {
      table.uuid('roomId').references('id').inTable('zn_chat_rooms').onDelete('CASCADE')
      table.uuid('userId').references('id').inTable('zn_users').onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.pivotTableName)

    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign('vendorId')
      table.dropColumn('vendorId')
    })
  }
}