import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_affiliate_commissions'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('lastEmailedCommissionAmount', 15, 6)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('lastEmailedCommissionAmount')
    })
  }
}