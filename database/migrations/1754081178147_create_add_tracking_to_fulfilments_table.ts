import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_order_fulfillments'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dateTime('eta', { useTz: true }).nullable()
      table.dateTime('originalEta', { useTz: true }).nullable()
      table.string('serviceLevel').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('eta')
      table.dropColumn('originalEta')
      table.dropColumn('serviceLevel')
    })
  }
}
