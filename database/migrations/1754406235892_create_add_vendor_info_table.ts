import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_vendors'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('thumbnailId').references('id').inTable('zn_medias').onDelete("SET NULL")
      table.text('description')
      table.text('returnWarrantyPolicy')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('returnWarrantyPolicy')
      table.dropColumn('description')

      table.dropForeign('thumbnailId')
      table.dropColumn('thumbnailId')
    })
  }
}