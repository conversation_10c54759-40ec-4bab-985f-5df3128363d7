import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_package_tracking_histories'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.string('fulfilId').notNullable()
      table.string('trackingNumber').notNullable()
      table.string('carrier').notNullable()
      table.string('objectId').notNullable()
      table.string('status').notNullable()
      table.string('statusDetails').nullable()
      table.timestamp('statusDate', { useTz: true }).notNullable()
      table.timestamp('objectCreated', { useTz: true }).notNullable()
      table.string('city').nullable()
      table.string('state').nullable()
      table.string('zip').nullable()
      table.string('country', 5).nullable()

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
