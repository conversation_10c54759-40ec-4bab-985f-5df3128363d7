import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_inventories'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('shopifyInventoryLevelId')
      table.uuid('warehouseId').references('id').inTable('zn_warehouses').onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('shopifyInventoryLevelId')
      table.dropForeign('warehouseId')
      table.dropColumn('warehouseId')
    })
  }
}