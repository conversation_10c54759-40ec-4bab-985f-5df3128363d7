import AppMail from "#mails/app_mail";
import ZnVendor from "#models/zn_vendor";
import env from "#start/env";

export default class VendorRegistrationNotification extends AppMail {
  subject = "[Vendor] New registration"

  constructor(
    private vendor: ZnVendor,
    private adminName: string,
    private email: string,
  ) {
    super()
  }

  prepare() {
    this.message
      .htmlView('mails/vendors/admin_registration_notification.edge', {
        serverDomain: this.baseUrl,
        vendor: this.vendor,
        adminName: this.adminName,
        applicationUrl: `${env.get('BE_BASE_URL')}/managements-vendors/${this.vendor.id}`
      })
      .to(this.email)
  }
}
