import AppMail from "#mails/app_mail";
import env from "#start/env";

export default class EscalateNotification extends AppMail {

  private linkToChatRoom: string

  constructor(
    private adminName: string,
    private adminEmail: string,
    private userName: string,
    private roomId: string,
  ) {
    super()
    const BE_URL = env.get('BE_BASE_URL') ?? ""
    this.linkToChatRoom = `${BE_URL}/chatbot/${this.roomId}`
  }

  prepare() {
    this.message
      .subject(`URGENT -  Customer‑Support ASAP: ${this.userName} `)
      .htmlView('mails/chatbot/escalate_notification', {
      serverDomain:   this.baseUrl,       // for @layout.home
      userName:       this.userName,      // shown in heading & body
      adminName:      this.adminName,     // greeting
      roomId:         this.roomId,        // displayed under link
      linkToChatRoom: this.linkToChatRoom // <a> target
    })
      .to(this.adminEmail)
  }
}
