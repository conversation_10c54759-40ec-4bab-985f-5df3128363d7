import AppMail from '#mails/app_mail';

export default class CommissionRejectedNotification extends AppMail {
  constructor(
    private userFirstName: string,
    private commissionId: string,
    private orderTime: string,
    private audienceName: string,
    private orderTotal: string,
    private commissionAmount: string,
    private rejectReason: string | null,
    private userEmail: string,
  ) {
    super()
  }

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */
  prepare() {
    this.message
      .subject('Notice: Commission Not Approved')
      .htmlView('mails/affiliation/commission_rejected', {
        serverDomain: this.baseUrl,
        userFirstName: this.userFirstName,
        commissionId: this.commissionId,
        orderTime: this.orderTime,
        audienceName: this.audienceName,
        orderTotal: this.orderTotal,
        commissionAmount: this.commissionAmount,
        rejectReason: this.rejectReason,
      })
      .to(this.userEmail)
  }
}
