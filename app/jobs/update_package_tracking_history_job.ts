import { Job } from '@rlanz/bull-queue'
import {PackageTrackingService} from "../services/shippo/package_tracking_service.js";

interface UpdatePackageTrackingHistoryJobPayload {
  tracking: any | null,
  trackingNumber: any | null,
  trackingCompany: any | null,
  from: string
}

export default class UpdatePackageTrackingHistoryJob extends Job {
  packageTrackingService = new PackageTrackingService()
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: UpdatePackageTrackingHistoryJobPayload) {
    const { tracking, trackingCompany, trackingNumber, from } = payload

    if(from === 'webhook' && tracking) {
      await this.packageTrackingService.updateTrackingsWebhook(tracking)
    } else {
      const trackingData = await this.packageTrackingService.getTrackings(trackingCompany, trackingNumber)
      await this.packageTrackingService.updateTrackingsFromApi(trackingData)
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: UpdatePackageTrackingHistoryJobPayload) {}
}
