import { FulfilService } from '#services/fulfil_service'
import { Job } from '@rlanz/bull-queue'

interface CreateFulfilProductJobPayload {
  fulfilProductData: any
}

export default class CreateFulfilProductJob extends Job {
  private fulfilService = new FulfilService()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ fulfilProductData }: CreateFulfilProductJobPayload) {
    await this.fulfilService.pushProductWithBrand(fulfilProductData)
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: CreateFulfilProductJobPayload) { }
}
