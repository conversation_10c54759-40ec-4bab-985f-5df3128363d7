import { EAIAssistantRole } from '#models/zn_ai_assistant'
import { Job } from '@rlanz/bull-queue'
import OpenAI from 'openai'
import pLimit from 'p-limit'
import path from 'path'
import { writeFile } from 'fs/promises'

interface GenerateQAPairsJobPayload {
  vectorStoreId: string
  assistantRole: string
  numOfPairs: number
}

export default class GenerateQAPairsJob extends Job {
  private openai = new OpenAI()
  static get $$filepath() {
    return import.meta.url
  }

  async handle(payload: GenerateQAPairsJobPayload) {
    const { vectorStoreId, assistantRole, numOfPairs } = payload
    let role: EAIAssistantRole
    switch (assistantRole) {
      case 'CUSTOMER_SERVICE':
        role = EAIAssistantRole.CUSTOMER_SERVICE
        break
      case 'SHOPPING_ASSISTANT':
        role = EAIAssistantRole.SHOPPING_ASSISTANT
        break
      case 'POST_ASSISTANT':
        role = EAIAssistantRole.POST_ASSISTANT
        break
      case 'ORDER_ASSISTANT':
        role = EAIAssistantRole.ORDER_ASSISTANT
        break
      case 'RECEPTIONIST_ASSISTANT':
        role = EAIAssistantRole.RECEPTIONIST_SERVICE
        break
      default:
        role = EAIAssistantRole.CUSTOMER_SERVICE
        break
    }
    const systemsPromptMap: Record<EAIAssistantRole, string> = {
      [EAIAssistantRole.CUSTOMER_SERVICE]:
        'You are ZURNO CUSTOMER CARE, frontline support for Zurno Inc. (B2B beauty‑supply).',
      [EAIAssistantRole.POST_ASSISTANT]: '',
      [EAIAssistantRole.ORDER_ASSISTANT]:
        "You are an **order tracking assistant**. Your task is to provide clear, accurate information about customers' orders based on the text summaries supplied by our backend.",
      [EAIAssistantRole.RECEPTIONIST_SERVICE]: '',
      [EAIAssistantRole.SHOPPING_ASSISTANT]:
        'You are the in-app **smart shopping assistant** for **Zurno Inc.**, specializing in salon-grade nail supplies, lash kits, gel polish, and related items.',
    }


    const instruction =
      '' +
      'You are an expert instructional designer tasked with turning the provided source material into self-contained Question–Answer (QA) pairs for study and assessment.\n' +
      '\n' +
      '## Objective\n' +
      '1. Read each document carefully.\n' +
      '2. Extract the most important, test-worthy facts, concepts, definitions, and relationships.\n' +
      '3. For every important point, write a clear question that a diligent reader *should* be able to answer after studying the document.\n' +
      '4. Immediately follow each question with a concise, correct answer that refers only to information found in the document itself (no outside knowledge).\n' +
      '\n' +
      '## Output format\n' +
      'Return the result as **newline-delimited JSON (JSONL)**.\n' +
      'Write **one JSON object per line** that looks like this:\n' +
      '\n' +
      '{"messages":[\n' +
      `   {"role":"system","content":"${systemsPromptMap[role]}"},\n` +
      '   {"role":"user","content":"<question>"},\n' +
      '   {"role":"assistant","content":"<answer>"}\n' +
      ' ]}\n' +
      '\n' +
      '**Rules**\n' +
      '\n' +
      '- Do **not** wrap the whole set in `[]` or add commas between records.\n' +
      '- Do **not** print anything before or after the JSONL block.\n' +
      '- Escape internal quotes as needed so each line is valid JSON.\n' +
      `- Generate exactly ${Math.min(16, numOfPairs)} QA pairs per response.\n`

    console.log(instruction)
    const estimatedPairsPerCall = 2 // Conservative estimate based on your logs
    const callsNeeded = Math.ceil(numOfPairs / estimatedPairsPerCall)
    const maxConcurrentCalls = 8

    const limit = pLimit(maxConcurrentCalls)
    const tasks = Array.from({ length: callsNeeded }, (_, index) =>
      limit(() =>
        this.openai.responses.create({
          model: 'o4-mini',
          temperature: 1.0,
          instructions: instruction,
          input: `Generate QA pairs that ${EAIAssistantRole} from the knowledge base . Batch ${index + 1} of ${callsNeeded}.`,
          tools: [
            {
              type: 'file_search',
              vector_store_ids: [vectorStoreId],
            },
          ],
        })
      )
    )

    const responses = await Promise.all(tasks)

    // Extract and count actual QA pairs
    let totalPairs = 0
    const allQAPairs = []

    for (const response of responses) {
      if (response.output_text) {
        const lines = response.output_text
          .trim()
          .split('\n')
          .filter((line) => line.trim())
        totalPairs += lines.length
        allQAPairs.push(...lines)
      }
    }

    console.log(`Generated ${totalPairs} QA pairs (requested: ${numOfPairs})`)

    // If we need more pairs, you might want to make additional calls here
    if (totalPairs < numOfPairs) {
      console.log(
        `Warning: Generated fewer pairs than requested. Consider increasing calls or adjusting the prompt.`
      )
    }

    const outputDir = path.join(process.cwd(), 'app', 'services', 'chatbot', 'documents')
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
    const filename = `qa_pairs_${assistantRole}_${timestamp}.jsonl`
    const filePath = path.join(outputDir, filename)
    const fileContent = allQAPairs.join('\n')
    await writeFile(filePath, fileContent)
    console.log(`QA pairs saved to: ${filePath}`)
    console.log(`File contains ${allQAPairs.length} QA pairs`)
  }
  async rescue(_: GenerateQAPairsJobPayload) {}
}
