import { OrderService } from '#services/shop/order_service';
import { Job } from '@rlanz/bull-queue';

interface WebhookShopifyFulfillmentUpdateJobPayload {
  payload: any,
  topic: string,
}

export default class WebhookShopifyFulfillmentUpdateJob extends Job {
  private orderService = new OrderService()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ payload }: WebhookShopifyFulfillmentUpdateJobPayload) {
    await this.orderService.updateOrderFulfillment(payload);
  }

  async rescue() { }
}
