import ZnInventory from '#models/zn_inventory'
import ZnProductVariant from '#models/zn_product_variant'
import ZnWarehouse from '#models/zn_warehouse'
import { ShopifyService } from '#services/shopify/shopify_service'
import { Job } from '@rlanz/bull-queue'

interface WebhhokShopifyInventoryLevelJobPayload {
  inventoryLevel: any
}

export default class WebhookShopifyInventoryLevelJob extends Job {
  private shopifyService = new ShopifyService()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ inventoryLevel }: WebhhokShopifyInventoryLevelJobPayload) {
    const shopifyInventoryLevel = await this.shopifyService.getInventoryItemById(inventoryLevel.admin_graphql_api_id)

    const dbInventory = await ZnInventory.findBy({ shopifyInventoryLevelId: shopifyInventoryLevel.id })

    const quantityAvailable = shopifyInventoryLevel.quantities.find((quantity: any) => quantity.name == 'available')?.quantity || 0
    const quantityOnHand = shopifyInventoryLevel.quantities.find((quantity: any) => quantity.name == 'on_hand')?.quantity || 0

    if (dbInventory) {
      dbInventory.quantityAvailable = quantityAvailable
      dbInventory.quantityOnHand = quantityOnHand
      await dbInventory.save()

    } else {
      const dbVariant = await ZnProductVariant.query()
        .preload('product')
        .where({ shopifyVariantId: shopifyInventoryLevel.item?.variant?.id })
        .first()

      const dbWarehouse = await ZnWarehouse.findBy({ shopifyLocationId: shopifyInventoryLevel.location?.id })

      if (dbVariant && dbWarehouse) {
        await ZnInventory.create({
          shopifyInventoryLevelId: shopifyInventoryLevel.id,

          quantityAvailable: quantityAvailable,
          quantityOnHand: quantityOnHand,

          warehouseId: dbWarehouse.id,
          variantId: dbVariant.id,

          variantSku: dbVariant.sku || "",
          variantName: dbVariant.title || "",

          productName: dbVariant.product?.title,
        })
      }
    }

  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: WebhhokShopifyInventoryLevelJobPayload) { }
}
