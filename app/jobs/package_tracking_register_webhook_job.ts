import { PackageTrackingService } from '#services/shippo/package_tracking_service'
import { Job } from '@rlanz/bull-queue'

interface PackingTrackingRegisterWebhookJobPayload {
  data: any,
}

export default class PackingTrackingRegisterWebhookJob extends Job {
  private packageTrackingService = new PackageTrackingService()
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ data }: PackingTrackingRegisterWebhookJobPayload) {
    await this.packageTrackingService.registerWebhook(data)
  }

  async rescue() { }
}
