import { Job } from '@rlanz/bull-queue'
import ZnOrder from "#models/zn_order";
import {OrderService} from "../services/shop/order_service.js";

interface SeparateVendorOrderJobPayload {
  order: ZnOrder,
  action: string
}

export default class SeparateVendorOrderJob extends Job {
  private orderService = new OrderService()
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: SeparateVendorOrderJobPayload) {
    const { order, action } = payload
    if(action == 'create') {
      await this.orderService.separateVendorOrder(order)
    } else {
      await this.orderService.updateVendorOrder(order)
    }

  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SeparateVendorOrderJobPayload) {}
}
