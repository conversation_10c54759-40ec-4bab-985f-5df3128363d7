import {BaseEmbeddingJob} from "#jobs/base_embedding_job";
import ZnPost from "#models/zn_post";
import DensePostEmbeddingService from "../services/pinecone/dense_post_embedding_service.js";

export default class UpdateDensePostEmbeddingJob extends BaseEmbeddingJob<ZnPost> {
  protected embeddingService = new DensePostEmbeddingService()

  static get $$filepath() { return import.meta.url }

  protected lookup(ids: string[]) {
    return ZnPost.query()
      .whereIn('id', ids)
      .preload('categories')
      .preload('city')
      .preload('state')
      .preload('store')
      .preload('country')
      .preload('translations')
      .preload('product')
      .preload('timelines', (timelineQuery) => {
        timelineQuery.preload('variant', (variantQuery) => {
          variantQuery.preload('product')
        })
      })
  }
}

