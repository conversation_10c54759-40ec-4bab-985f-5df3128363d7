import ZnUser from '#models/zn_user'
import { HttpContext } from '@adonisjs/core/http'
import { CustomerService } from '../../../../services/customer/customer_service.js'
import {
  createCustomerValidator,
  updateCustomerValidator,
} from '../../../validators/customer/customer_validator.js'
import { DateTime } from 'luxon'

export default class CustomerController {
  private customerService: CustomerService

  constructor() {
    this.customerService = new CustomerService()
  }

  /**
   * @index
   * @tag Customer Management
   * @summary List all customers
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnUser[]>.paginated() - List of customers
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, storeId, gender, sort, order } = request.qs()

      if (!storeId) {
        return response.badRequest({ message: 'Store ID is required' })
      }

      const result = await this.customerService.searchCustomers(storeId, {
        search,
        page: Number(page),
        limit: Number(limit),
        gender,
        sort,
        order,
      })

      return response.ok(result)
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to retrieve customers',
        error: error.message,
      })
    }
  }

  /**
   * @show
   * @tag Customer Management
   * @summary Get customer details
   * @paramPath id - ID of Customer - @type(string) @required
   * @responseBody 200 - <ZnUser> - Customer details
   * @responseBody 404 - {"message":"Customer not found"} - Not Found
   */
  async show({ params, response }: HttpContext) {
    try {
      const { id } = params

      if (!id || typeof id !== 'string') {
        return response.badRequest({ message: 'Valid customer ID is required' })
      }

      const customer = await ZnUser.query()
        .where('id', id)
        .preload('avatarMedia')
        .preload('defaultAddress')
        .preload('customerStores')
        .first()

      if (!customer) {
        return response.notFound({
          message: 'Customer not found or not associated with this store',
        })
      }

      return response.ok(customer)
    } catch (error) {
      console.error('Error retrieving customer details:', error)
      return response.internalServerError({
        message: 'Failed to retrieve customer details',
        error: error.message,
      })
    }
  }

  /**
   * @store
   * @tag Customer Management
   * @summary Create a new customer or associate existing user with store
   * @requestBody <createCustomerValidator> - Customer data
   * @responseBody 201 - <ZnUser> - Created customer
   * @responseBody 200 - <ZnUser> - Associated existing user as customer
   * @responseBody 400 - {"message":"Validation failed","errors":[]} - Validation Error
   */
  async store({ request, response }: HttpContext) {
    try {
      let storeId = null
      // Validate input data
      let payload
      try {
        payload = await request.validateUsing(createCustomerValidator)

        storeId = payload.storeId

        if (!storeId) {
          return response.badRequest({ message: 'Store ID is required' })
        }
      } catch (validationError) {
        return response.unprocessableEntity(validationError.messages)
      }

      // Check for required fields
      if (!payload.firstName && !payload.lastName) {
        return response.badRequest({
          message: 'Validation failed',
          errors: {
            firstName: 'Either firstName or lastName is required',
          },
        })
      }

      if (!payload.email && !payload.phone) {
        return response.badRequest({
          message: 'Validation failed',
          errors: {
            email: 'Either email or phone is required',
          },
        })
      }

      const { address, storeId: _, ...customerData } = payload

      const typedCustomerData: Partial<ZnUser> = {
        ...customerData,
        birthday: undefined,
      }

      if (customerData.birthday) {
        typedCustomerData.birthday = DateTime.fromISO(customerData.birthday as string)
      }

      const { customer, isNewCustomer } = await this.customerService.createCustomer(
        typedCustomerData,
        storeId
      )

      if (payload.address) {
        const addressData = {
          ...payload.address,
          userId: customer.id,
        }

        const address = await customer.related('addresses').create(addressData)
        await customer.merge({ defaultAddressId: address.id }).save()
        await customer.load('defaultAddress')
      }

      if (payload.avatarId) {
        await customer.merge({ avatarId: payload.avatarId }).save()
      }

      if (isNewCustomer) {
        return response.created({
          customer,
          message: 'Customer created successfully',
        })
      } else {
        return response.ok({
          customer,
          message: 'Existing user associated as customer',
        })
      }
    } catch (error) {
      // Handle validation errors
      if (error.status === 422) {
        return response.unprocessableEntity(error.messages)
      }

      // Handle duplicate entry errors
      if (error.code === 'ER_DUP_ENTRY') {
        return response.conflict({
          message: 'Customer with this email or phone already exists',
          error: error.message,
        })
      }

      console.error('Error creating customer:', error)
      return response.internalServerError({
        message: 'Failed to create customer',
        error: error.message,
      })
    }
  }

  /**
   * @update
   * @tag Customer Management
   * @summary Update customer details
   * @paramPath id - ID of Customer - @type(string) @required
   * @requestBody <updateCustomerValidator> - Customer data
   * @responseBody 200 - <ZnUser> - Updated customer
   * @responseBody 404 - {"message":"Customer not found"} - Not Found
   * @responseBody 400 - {"message":"Validation failed","errors":[]} - Validation Error
   */
  async update({ params, request, response }: HttpContext) {
    try {
      const customerId = params.id

      // Validate customer ID
      if (!customerId || typeof customerId !== 'string') {
        return response.badRequest({ message: 'Valid customer ID is required' })
      }

      // Check if customer exists
      const customer = await ZnUser.query().where('id', customerId).first()

      if (!customer) {
        return response.notFound({
          message: 'Customer not found or not associated with this store',
        })
      }

      // Validate input data
      let payload
      try {
        payload = await request.validateUsing(updateCustomerValidator(customerId))
      } catch (validationError) {
        return response.unprocessableEntity(validationError.messages)
      }

      const { address, ...updateData } = payload

      const typedUpdateData: Partial<ZnUser> = {
        ...updateData,
        birthday: undefined,
      }

      if (updateData.birthday) {
        typedUpdateData.birthday = DateTime.fromISO(updateData.birthday as string)
      }

      const updatedCustomer = await this.customerService.updateCustomer(customerId, typedUpdateData)

      if (payload.address) {
        const addressData = {
          ...payload.address,
          userId: customerId,
        }

        const existingAddress = await customer.related('defaultAddress').query().first()

        if (existingAddress) {
          await existingAddress.merge(addressData).save()
        } else {
          const address = await customer.related('addresses').create(addressData)
          await updatedCustomer.merge({ defaultAddressId: address.id }).save()
        }
      }

      if (payload.avatarId) {
        await updatedCustomer.merge({ avatarId: payload.avatarId }).save()
      }

      await updatedCustomer.load('defaultAddress')
      await updatedCustomer.load('avatarMedia')

      return response.ok({
        customer: updatedCustomer,
        message: 'Customer updated successfully',
      })
    } catch (error) {
      // Handle validation errors
      if (error.status === 422) {
        return response.unprocessableEntity(error.messages)
      }

      // Handle duplicate entry errors
      if (error.code === 'ER_DUP_ENTRY') {
        return response.conflict({
          message: 'Customer with this email or phone already exists',
          error: error.message,
        })
      }

      console.error('Error updating customer:', error)
      return response.internalServerError({
        message: 'Failed to update customer',
        error: error.message,
      })
    }
  }

  /**
   * @destroy
   * @tag Customer Management
   * @summary Delete a customer (soft delete)
   * @paramPath id - ID of Customer - @type(string) @required
   * @responseBody 200 - {"message":"Customer deleted successfully"} - Success
   * @responseBody 404 - {"message":"Customer not found"} - Not Found
   */
  async destroy({ params, response }: HttpContext) {
    try {
      const customerId = params.id

      // Check if customer exists and belongs to the store
      const customer = await ZnUser.query().where('id', customerId).first()

      if (!customer) {
        return response.notFound({
          message: 'Customer not found or not associated with this store',
        })
      }

      // Soft delete customer
      await this.customerService.deleteCustomer(customerId)

      return response.ok({ message: 'Customer deleted successfully' })
    } catch (error) {
      console.error('Error deleting customer:', error)
      return response.internalServerError({
        message: 'Failed to delete customer',
        error: error.message,
      })
    }
  }
}
