import Chat<PERSON><PERSON><PERSON><PERSON><PERSON> from '#jobs/chat_delete_job'
import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#jobs/chat_message_job'
import ZnChatMessage from '#models/zn_chat_message'
import ZnChatRoom from '#models/zn_chat_room'
import ZnUser from '#models/zn_user'
import { IvschatService } from '#services/aws/ivschat_service'
import JwtService from '#services/jwt_service'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'

export default class VendorChatController {
    private ivsChatService: IvschatService

    constructor() {
        this.ivsChatService = new IvschatService()
    }

    /**
     * @createChatRoom
     * @tag Vendor Chat
     * @summary Create Chat Room
     * @requestBody {"name":""}
     * @responseBody 200 - <ZnChatRoom> - Create Chat Room
     */
    async createChatRoom({ request, response }: HttpContext) {
        const data = request.body()

        const room = await this.ivsChatService.createRoom({ name: data.name })

        const dbRoom = await ZnChatRoom.create({
            arn: room.arn,
            name: room.name,
        })

        return response.ok(dbRoom)
    }

    /**
     * @createChatToken
     * @tag Vendor Chat
     * @summary Chat Token provider
     * @paramPath id - ID of Chat Room - @required
     * @responseBody 200 - {"token":"","tokenExpirationTime":"2025-02-18T22:44:57.000Z","tokenExpirationTime","2025-02-18T22:44:57.000Z"} - ChatToken provider for IVS ChatRoom
     */
    async createChatToken({ auth, params, request, response }: HttpContext) {
        try {
            const roomId = params.id

            const room = await ZnChatRoom.find(roomId)

            if (!room) { return response.notFound({ message: 'Chat Room not found' }) }

            let requestUser
            if (await auth.check()) {
                const currentVendorUser = auth.getUserOrFail() as ZnUser

                if (room.vendorId && room.vendorId != currentVendorUser?.vendorId) {
                    return response.badRequest("Cannot access this chat room")
                }

                if (currentVendorUser.avatarId) {
                    currentVendorUser.preload('avatarMedia')
                }

                requestUser = currentVendorUser
            }

            const { guestId } = request.qs()

            let attributes: any = {
                type: 'guest',
                guestId
            }

            if (requestUser) {
                attributes = {
                    type: 'user',
                    email: requestUser?.email,
                    firstName: requestUser?.firstName,
                    lastName: requestUser?.lastName,
                    fullname: requestUser?.fullname,
                    avatarUrl: requestUser?.avatarUrl,
                    avatarMediaUrl: requestUser?.avatarMedia?.url,
                    vendorId: requestUser?.vendorId,
                }
            }

            const token = await this.ivsChatService.createChatToken({
                roomArn: room.arn,
                userId: requestUser?.id || guestId || "guest",
                capabilities: ['SEND_MESSAGE'],
                attributes
            })

            return response.ok(token)

        } catch (error) {
            console.log(error);
            return response.internalServerError({
                message: "Something went wrong!",
                error
            })
        }
    }

    /**
     * @getMessages
     * @tag Vendor Chat
     * @summary Get Chat Room Messages
     * @paramPath id - ID of Chat Room - @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @responseBody 200 - <ZnChatMessage[]>.paginated() - Get Chat Room Messages, newest to oldest
     */
    async getMessages({ auth, params, request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            filterIvsIds = []
        } = request.qs()

        try {
            const roomId = params.id
            const room = await ZnChatRoom.query()
                .where({ id: roomId })
                .preload('vendor')
                .first()

            if (!room) { return response.notFound("Chat Room Not Found") }

            if (await auth.check()) {
                const currentVendorUser = auth.getUserOrFail() as ZnUser

                if (room.vendorId && room.vendorId != currentVendorUser?.vendorId) {
                    return response.badRequest("Cannot access this chat room")
                }
            }

            const messages = await ZnChatMessage.query()
                .where({ roomId })
                .preload('user', (userQuery) => {
                    userQuery.preload('avatarMedia')
                })
                .preload('admin', (userQuery) => {
                    userQuery.preload('avatar')
                })
                .whereNull('parentMessageId')
                .whereNotIn('ivsChatMessageId', filterIvsIds)
                .orderBy('createdAt', 'desc')
                .paginate(page, limit)

            return response.ok({
                ...messages.serialize(),
                room,
            })
        } catch (error) {
            console.log(error);
            return response.internalServerError("Something went wrong", error)
        }
    }

    /**
     * @createChatMessage
     * @tag Vendor Chat
     * @summary Create chat message
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"id":"wNbmMLv3EuWl","content":"A message","attributes":{"parentMessageIvsId":"LWuE3vLMmbNw"}}
     * @responseBody 200 - StreamCommentJob Sent. - Send job to save chat message as post comment
     */
    async createChatMessage({ params, request, response }: HttpContext) {
        const roomId = params.id

        try {
            const room = await ZnChatRoom.find(roomId)
            if (!room) { return response.notFound("Chat Room Not Found") }

            const data = request.body() as any

            let requestUserId
            const authToken = request.header('Authorization') as string
            JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
                if (decodedToken) {
                    requestUserId = decodedToken.userId
                }
            })

            await queue.dispatch(
                ChatMessageJob,
                {
                    roomId,
                    data: {
                        ...data,
                        sender: {
                            userId: requestUserId,
                        }
                    },
                },
                { queueName: 'liveStream' }
            )

            return response.ok("StreamCommentJob Sent.")

        } catch (error) {
            console.log(error);
            return response.internalServerError("Something went wrong", error)
        }
    }

    /**
     * @deleteChatMessage
     * @tag Vendor Chat
     * @summary Delete chat message
     * @paramPath id - IVS ID of Chat Message - @required @example(wNbmMLv3EuWl)
     * @responseBody 200 - StreamChatDeleteJob Sent. - Delete Chat Message
     * @responseBody 403 - Cannot delete others messages - Forbidden access
     * @responseBody 404 - Chat Message Not Found - Not Found
     */
    async deleteChatMessage({ auth, params, response }: HttpContext) {
        const ivsChatMessageId = params.id

        const message = await ZnChatMessage.findBy({ ivsChatMessageId })

        if (!message) { return response.notFound("Chat Message Not Found") }

        const currentUser = auth.getUserOrFail() as ZnUser

        if (message.userId != currentUser.id) { return response.forbidden("Cannot delete others messages") }

        await queue.dispatch(
            ChatDeleteJob,
            { ivsChatMessageId },
            { queueName: 'liveStream' }
        )

        return response.ok("StreamChatDeleteJob Sent.")
    }

    /**
     * @listChatRooms
     * @tag Vendor Chat
     * @summary List chat message
     * @paramPath id - IVS ID of Chat Message - @required @example(wNbmMLv3EuWl)
     * @responseBody 200 - StreamChatDeleteJob Sent. - Delete Chat Message
     * @responseBody 403 - Cannot delete others messages - Forbidden access
     * @responseBody 404 - Chat Message Not Found - Not Found
     */
    async listChatRooms({ request, response }: HttpContext) {
        try {
            const {
                page = 1,
                limit = 10,
                vendorId
            } = request.qs()

            // return rooms with latest message first, then latest newly created rooms
            const roomsFilterQuery = db.query()
                .from('zn_chat_rooms')
                .whereNull('zn_chat_rooms.deletedAt')
                .leftJoin('zn_chat_messages', 'zn_chat_rooms.id', 'zn_chat_messages.roomId')
                .whereNull('zn_chat_messages.deletedAt')
                .groupBy('zn_chat_rooms.id')
                .orderByRaw('COALESCE(MAX(zn_chat_messages.createdAt), zn_chat_rooms.updatedAt) DESC')
                .select(
                    'zn_chat_rooms.*',
                    db.raw('MAX(zn_chat_messages.createdAt) AS latestMessageAt'),
                )

            if (vendorId) {
                roomsFilterQuery.where('zn_chat_rooms.vendorId', vendorId)
            }

            const roomsFilter = await roomsFilterQuery
                .paginate(page, limit)

            const roomsInfo = await ZnChatRoom.query()
                .whereIn('id', roomsFilter.map(room => room.id))
                .preload('latestMessage', (messageQuery) => {
                    messageQuery
                        .preload('user')
                        .preload('admin')
                })
                .preload('vendor')
                .orderBy('updatedAt', 'desc')

            const meta = roomsFilter.toJSON().meta
            const data = roomsFilter.toJSON().data

            const newData = []
            for (const datum of data) {
                const newInfo = roomsInfo.find(info => info.id == datum.id)

                const newDatum = {
                    ...datum,
                    ...newInfo?.serialize(),
                }

                newData.push(newDatum)
            }

            return response.ok({
                meta: meta,
                data: newData
            })

        } catch (error) {
            console.log(error);
            return response.internalServerError({
                message: "Something went wrong!",
                error
            })
        }

    }
}
