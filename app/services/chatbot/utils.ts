import {AgentResponse} from "./chatbot_agent_interface.js";
import {AdminNotificationService} from "../../../admin/services/notification/admin_notification_service.js";
import {ACTION, RESOURCE} from "#constants/authorization";
import mail from "@adonisjs/mail/services/main";
import EscalateNotification from "#mails/chatbot/escalate_notification";
import logger from "@adonisjs/core/services/logger";
import {NOTIFICATION_TYPE} from "#constants/notification";

/** Strip markdown fences / citations and JSON-parse when possible. */
export function sanitizeAssistantText(raw: string): AgentResponse {
  let cleaned = raw
    .replace(/^```json/i, '')
    .replace(/^```/, '')
    .replace(/```$/, '')
    .replace(/\[\d+:\d+†[^\]]+\]/g, '')
    .replace(/【[^】]+】/g, '')
    .trim()

  let parsed: AgentResponse
  try {
    parsed = JSON.parse(cleaned)
  } catch {
    parsed = {
      text: cleaned,
      assistantId: '',   // concrete agent fills this later
      productIds: [],
      collectionIds: [],
      postIds: [],
      questions: [],
    }
  }

  // Normalise & tidy arrays
  parsed.productIds    = (parsed.productIds    ?? []).map((id) => id.replace(/^product_/, '').trim())
  parsed.collectionIds = (parsed.collectionIds ?? []).map((id) => id.replace(/^collection_/, '').trim())
  parsed.postIds       = (parsed.postIds       ?? []).map((id) => id.replace(/^post_/, '').trim())
  parsed.questions     = (parsed.questions     ?? []).map((q)  => q.trim())

  return parsed
}

export function shouldEscalate(response: AgentResponse): boolean {
  return Array.isArray(response.action) && response.action.includes('escalate')
}

export async function handleEscalation(
  roomId: string,
  userName: string,
  content: string,
  ivsID: string
) {
  const adminNotificationService = new AdminNotificationService()
  const admins = await adminNotificationService.getAdminsByPermissions([
    {action: ACTION.READ, resource: RESOURCE.CHATBOT}
  ])

  admins.forEach(async (admin) => {
    await adminNotificationService.sendNotification({
      adminId: admin.id,
      resourceId: roomId,
      type: NOTIFICATION_TYPE.CHATBOT,
      title: `${userName} is waiting for support`,
      description: content,
      rootResourceId: ivsID
    })

    await mail
      .send(
        new EscalateNotification(
           admin.name ?? 'Admin',
          admin.username,
          userName,
          roomId
        )
      )
      .then(() => {
        logger.info(`Escalation email has been sent successfully to ${admin.username}`)
      })
      .catch((error) => {
        logger.error('Error when sending email', error)
      })
  })

}
