import { EAIAssistantRole }    from '#models/zn_ai_assistant'
import {<PERSON><PERSON><PERSON><PERSON>, AgentResponse} from '#services/chatbot/chatbot_agent_interface'
import { BaseChatbotAgent }    from '#services/chatbot/base_chatbot_agent'
import {AgentSettings} from "./chatbot_agent_interface.js";
import {loadPrompt} from "./prompt_loader.js";
import env from "#start/env";
import {Tool} from "openai/resources/responses/responses";

/**
 *  Customer-Service Agent
 *  ──────────────────────
 *  • <PERSON>les site-feedback, policy questions, chat issues, etc.
 *  • No database look-ups (so: no pre-context, no post-process).
 *  • Relies entirely on its OpenAI Assistant for answers.
 */


export default class CustomerServiceAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.CUSTOMER_SERVICE
  constructor( assistantId: string, openAIId: string ) {

    const instruction = loadPrompt('customer_service')
    const vectorFileId = env.get('CUSTOMER_SERVICE_FILE_ID')
    let tools : Tool[] = []
    if (vectorFileId) {
      tools.push(
        {
          type: "file_search",
          vector_store_ids: [vectorFileId],
        }
      )
    }
    const agentSettings: AgentSettings = {
      model: "gpt-5-nano",
      reasoning: {
        effort: "low"
      },
      text: {
        verbosity: "low"
      },
      tools
    }

    super(assistantId, openAIId, instruction, agentSettings)
  }

  protected async getCompleteResponse(userMessage: string, lastMessage: string, assistantId: string) : Promise<AgentResponse> {
    const [questions, shouldEscalate] = await Promise.all([
      this.getQuestions(lastMessage),
      this.shouldEscalate(userMessage,lastMessage)
    ])

    console.log(questions)
    let actions : string[] = []
    if (shouldEscalate==='yes') {
      actions.push('escalate')
    }
    return {
      text: lastMessage,
      productIds: [],
      collectionIds: [],
      postIds: [],
      questions: questions,
      assistantId: assistantId,
      action: actions
    }
  }

  protected async shouldEscalate(userMessage: string, lastMessage: string) {
    const chatResponse = await this.openai.responses.create({
      model: 'gpt-4.1',
      instructions: "Assistant response: " + lastMessage + "User Message: " + userMessage,
      input: "Determine if the user needs to see the customer service agent. Returns ONE WORD, either yes or no ",
      temperature: 0.1
    })
    console.log('Should Escalate' , chatResponse)
    return chatResponse.output_text
  }
  needsPreContext(): boolean { return false }

  needsPostProcess(_firstResponse: AgentResponse): boolean { return false}

  async postProcess(_firstMessage: AgentResponse, _init: AgentInit): Promise<void> {}
}
