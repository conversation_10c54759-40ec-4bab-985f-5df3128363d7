import CreateFulfilProductJob from '#jobs/create_fulfil_product_job'
import ZnChannel from '#models/zn_channel'
import ZnInventory from '#models/zn_inventory'
import ZnProduct from '#models/zn_product'
import ZnProductImage from '#models/zn_product_image'
import ZnProductVariant from '#models/zn_product_variant'
import ZnVideoTimeline from '#models/zn_video_timeline'
import ZnWarehouse from '#models/zn_warehouse'
import { FastBundleService } from '#services/shopify/fast_bundle_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import StoreShopifyProduct from '#services/sync/store_shopify_product'
import VendorService from '#services/vendors/vendor_service'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import { getIdFromShopifyId } from '../../../services/commons.js'

export class ProductService {
  private shopifyService = new ShopifyService()
  private storeShopifyProduct = new StoreShopifyProduct()

  async productIdsByShopifyCollectionId(shopifyCollectionIds: string[]) {
    const productIds = (
      await db
        .from('zn_product_collections')
        .join('zn_collections', 'zn_product_collections.collectionId', 'zn_collections.id')
        .whereIn('zn_collections.shopifyCollectionId', shopifyCollectionIds)
        .select('zn_product_collections.productId as id')
    ).map((p) => p.id)

    return productIds
  }

  async productIdsByCollectionId(collectionId: string) {
    const productIds = (
      await db
        .from('zn_product_collections')
        .join('zn_collections', 'zn_product_collections.collectionId', 'zn_collections.id')
        .where('zn_collections.id', collectionId)
        .select('zn_product_collections.productId as id')
    ).map((p) => p.id)

    return productIds
  }

  async getProductIdWithOrderByPrice(direction: 'asc' | 'desc') {
    const orderByProductIds = (
      await db
        .from('zn_products')
        .leftJoin('zn_product_variants', 'zn_products.id', 'zn_product_variants.productId')
        .select('zn_products.id')
        .groupBy('zn_products.id')
        .orderByRaw(`MIN(zn_product_variants.price) ${direction}`)
    ).map((item) => item.id)

    return orderByProductIds as string[]
  }

  async getProductById(id: string) {
    const product = await ZnProduct.query()
      .where((queryBuilder) => {
        queryBuilder.where('id', id).orWhere('shopifyProductId', `gid://shopify/Product/${id}`)
      })
      .whereNot('status', 'draft')
      .where('isGift', false)
      .preload('variants', (query) => {
        query.preload('image').preload('optionValues')
      })
      .preload('reviews', (query) => {
        query.limit(2).orderBy('createdAt', 'desc')
      })
      .preload('collections', (query) => {
        query.limit(1)
      })
      .preload('vendor')
      .preload('images')
      .preload('category')
      .preload('options', (query) => {
        query.preload('variantOptionValues').preload('productOptionValues')
      })
      .whereHas('channels', (channelQuery) => {
        channelQuery.where('isMobile', true).where('active', true)
      })
      .first()

    // update variant to not available for sale if main warehouse inventory is empty
    if (product) {
      const mainWarehouse = await ZnWarehouse.findBy({ isPrimary: true })

      let inventories: ZnInventory[] = []
      if (mainWarehouse) {
        inventories = await ZnInventory.query()
          .where({ warehouseId: mainWarehouse.id })
          .whereIn('variantId', product.variants.map(vari => vari.id))
          // only shopify
          .whereNotNull('shopifyInventoryLevelId')
      }

      for (const variant of product.variants) {
        if (product.pickupOnly) {
          variant.inventoryQuantity = 0
          variant.availableForSale = false
        } else {
          const mainWarehouseInventory = inventories.find(inv => inv.variantId == variant.id)

          if (mainWarehouseInventory && mainWarehouseInventory.quantityAvailable <= 0) {
            variant.availableForSale = false
          }
        }
      }
    }

    if (product && product.pickupOnly) {
      product.variants.forEach((variant) => {
        // ensure the property exists and override if needed
        variant.inventoryQuantity = 0
        variant.availableForSale = false
      })
    }

    return product
  }

  getProductsByIds(ids: string[]) {
    return ZnProduct.query()
      .whereIn('id', ids)
      .preload('variants', (query) => {
        query.preload('image').preload('optionValues')
      })
      .preload('reviews', (query) => {
        query.limit(2).orderBy('createdAt', 'desc')
      })
      .preload('image')
      .preload('vendor')
      .preload('images')
      .preload('reviewsSummary')
      .preload('options', (query) => {
        query.preload('variantOptionValues').preload('productOptionValues')
      })
      .whereHas('channels', (channelQuery) => {
        channelQuery.where('isMobile', true).where('active', true)
      })
      .orderByRaw('RAND()')
  }

  async getMainProductId(product: ZnProduct) {
    const bundleService = new FastBundleService()
    let mainProductId = product.id
    let bundleId = null
    if (await this.checkIsBundleProduct(product)) {
      const productId = getIdFromShopifyId(product.shopifyProductId)
      const bundle = await bundleService.getBundleByBundleProductId(productId)
      const item = bundle?.items?.find((item: any) => item.show_in_page)
      if (item) {
        mainProductId = item.id
        bundleId = bundle.id
      }
    }
    return { mainProductId, bundleId }
  }
  async checkIsBundleProduct(product: ZnProduct) {
    await product.load('tags')
    const tagNames = product?.tags?.map((tag: any) => tag.name.toLowerCase().trim()) || []
    const bundleTagList = (process.env.PRODUCT_BUNDLE_TAG_NAME || '')
      .split(',')
      .map((tag) => tag.toLowerCase().trim())

    return tagNames.some((tag) => bundleTagList.includes(tag))
  }

  async productsByCollectionHandle(handle: string, limit = 10, page = 0) {
    const offset = (page >= 1 ? page - 1 : page) * limit

    // Shared subquery to avoid duplication
    const baseFilters = (query: any) => {
      query
        .join('zn_product_collections', 'zn_product_collections.productId', 'zn_products.id')
        .join('zn_collections', 'zn_product_collections.collectionId', 'zn_collections.id')
        .whereNull('zn_collections.deletedAt')
        .where('zn_collections.handle', handle)
        .whereNot('zn_products.status', 'draft')
        .whereNull('zn_products.deletedAt')
        .whereExists(
          db
            .from('zn_products_channels')
            .join('zn_channels', 'zn_products_channels.channelId', 'zn_channels.id')
            .select(1)
            .whereRaw('zn_products_channels.productId = zn_products.id')
            .whereNull('zn_channels.deletedAt')
            .where('zn_channels.isMobile', true)
            .where('zn_channels.active', true)
        )
    }

    const countQuery = db.from('zn_products')
    baseFilters(countQuery)
    countQuery.whereNull('zn_products.deletedAt')
    const countPromise = countQuery.countDistinct('zn_products.id as total').first()

    const productQuery = db.from('zn_products')
    baseFilters(productQuery)
    productQuery.orderBy('zn_product_collections.orderBy').offset(offset).limit(limit)

    const [rawProducts, totalCountRow] = await Promise.all([productQuery, countPromise])
    // console.log(rawProducts, totalCountRow)
    const productIds = rawProducts.map((p: any) => p.productId)

    const products = await ZnProduct.query()
      .whereIn('id', productIds)
      .preload('variants', (query) => {
        query.preload('image').preload('optionValues')
      })
      .preload('reviews', (query) => {
        query.limit(2).orderBy('createdAt', 'desc')
      })
      .preload('image')
      .preload('vendor')
      .preload('images')
      .preload('reviewsSummary')
      .preload('options', (query) => {
        query.preload('variantOptionValues').preload('productOptionValues')
      })

    // Preserve original order
    const orderedProducts = productIds.map((id) => products.find((p) => p.id === id))
    return {
      meta: {
        total: totalCountRow?.total || 0,
        perPage: limit,
        currentPage: page,
        lastPage: Math.ceil((totalCountRow?.total || 0) / limit),
      },
      data: orderedProducts,
    }
  }

  async getVideosByProductId(productId: string) {
    const variantIds = (
      await ZnProductVariant.query().where('productId', productId).select('id')
    ).map((v) => v.id)

    let videoResults: Array<{ timeline: any; post: any; stream?: any }> = []
    if (variantIds.length) {
      const timelines = await ZnVideoTimeline.query()
        .whereIn('variantId', variantIds)
        .preload('post', (query) => {
          query
            .whereIn('type', ['video'])
            .where('expired', false)
            .where('isUnlist', false)
            .where('isDraft', false)
            .preload('medias')
            .preload('thumbnail')
            .preload('user', (query) => {
              query.preload('affiliate')
            })
            .preload('timelines', (timelineQuery) => {
              timelineQuery.preload('variant')
            })
        })
        .preload('variant', (query) => {
          query.preload('image')
        })
        .orderBy('createdAt', 'desc')

      const timelineMap = new Map()
      for (const t of timelines) {
        if (t.post && !timelineMap.has(t.post.id)) {
          timelineMap.set(t.post.id, t)
        }
      }
      videoResults = Array.from(timelineMap.values()).map((t) => ({
        timeline: t,
        post: t.post,
        stream: t.post?.stream || null,
      }))
    }

    // const livePosts = await ZnPost.query()
    //   .where('productId', productId)
    //   .whereNull('deletedAt')
    //   .preload('stream')

    // const videoPostIds = new Set(videoResults.map((v) => v.post.id))
    // const liveResults = livePosts
    //   .filter((post) => !videoPostIds.has(post.id))
    //   .map((post) => ({
    //     timeline: null,
    //     post,
    //     stream: post.stream || null,
    //   }))

    return videoResults
  }

  async listVendors(params: { page: number, pageSize: number, search: string }) {
    const vendorService = new VendorService()
    const vendors = await vendorService.getAllVendors(params.page, params.pageSize, params.search)
    return vendors
  }

  async vendorCreateDbProduct(payload: any) {
    const product = await ZnProduct.create({
      title: payload.title,
      description: payload.description,
      status: 'draft',

      vendorId: payload.vendor?.id,
      categoryId: payload.category?.id,
    })

    await ZnProductVariant.createMany(payload.variants.map((variant: any, index: number) => ({
      productId: product.id,

      title: variant.title,

      position: index + 1,

      sku: variant.sku,
      barcode: variant.barcode,
      inventoryQuantity: variant.inventoryQuantity,
      inventoryPolicy: variant.inventoryPolicy,

      price: variant.price,
      compareAtPrice: variant.compareAtPrice,

      weight: variant.weight,
      weightUnit: variant.weightUnit,
    })))

    const { variantsData } = this.transformDataToShopifyProductInput(payload, 'create')

    if (payload.images && variantsData) {
      await this.saveImagesToDB(product.id, payload.images, variantsData)
    }

    return product
  }

  async vendorUpdateDbProduct(id: string, payload: any) {

    const product = await ZnProduct.find(id)

    if (!product) { return }

    await product.merge({
      title: payload.title,
      description: payload.description,
      status: payload.status,

      categoryId: payload.category?.id,
    }).save()

    return product
  }

  async createShopifyProduct(payload: any) {
    try {
      const { productData, optionsData, variantsData, mediaData } = this.transformDataToShopifyProductInput(payload, 'create')

      const productCreateData = {
        ...productData,

        productOptions: optionsData,

        metafields: [{
          key: 'db_uuid',
          namespace: 'ext',
          type: 'single_line_text_field',
          value: payload.id,
        }]
      }

      const shopifyProduct = await this.shopifyService.createProduct(productCreateData)

      const { product } = await this.shopifyService.createProductVariants(shopifyProduct.id, variantsData, mediaData)

      const [newProduct] = await this.storeShopifyProduct.handle([product])

      if (payload.images && variantsData) {
        await this.saveImagesToDB(newProduct.id, payload.images, variantsData)
      }

      // return newProduct



      // const shopifyProduct = await this.shopifyService.createProduct(productCreateData)

      // await this.shopifyService.createProductVariants(shopifyProduct.id, createdVariantsData)

      // const { mediaData, variantsData } = this.transformDataToShopifyProductInput(payload, 'update')

      // const productUpdateData = {
      //   ...productData,
      //   productOptions: optionsData,
      //   variants: variantsData,
      //   files: mediaData,
      // }

      // const updatedShopifyProduct = await this.shopifyService.updateProduct(shopifyProduct.id, productUpdateData)

      // const [updatedProduct] = await this.storeShopifyProduct.handle([updatedShopifyProduct])

      // if (payload.images && variantsData) {
      //   await this.saveImagesToDB(updatedProduct.id, payload.images, variantsData)
      // }

      const activeMobileChannel = await ZnChannel.query()
        .where({
          active: true,
          isMobile: true,
        })
        .first()

      if (activeMobileChannel) {
        await this.shopifyService.publishPublishable(
          newProduct.shopifyProductId, [{ publicationId: activeMobileChannel.shopifyChannelId }])
        await newProduct.related('channels').sync([activeMobileChannel.id])
      }

      return newProduct

    } catch (error) {
      console.log(error);
      throw error
    }
  }

  async updateShopifyProduct(productShopifyId: string, payload: any) {
    try {
      const { productData, optionsData, mediaData, variantsData } = this.transformDataToShopifyProductInput(payload, 'update')

      const productUpdateData = {
        ...productData,
        productOptions: optionsData,
        variants: variantsData,
        files: mediaData,
      }

      const shopifyProduct = await this.shopifyService.updateProduct(productShopifyId, productUpdateData)

      const [updatedProduct] = await this.storeShopifyProduct.handle([shopifyProduct])

      if (payload.images && variantsData) {
        await this.saveImagesToDB(updatedProduct.id, payload.images, variantsData)
      }

      return updatedProduct

    } catch (error) {
      console.log(error);
      throw error
    }
  }

  async createFulfilProduct(payload: any) {
    const { productData, variantsData } = this.transformDataToShopifyProductInput(payload, 'create')

    for (const variant of variantsData) {
      const variantTitle = variant.optionValues
        .map((opVal: any) => opVal.name)
        .join(' / ')

      const fulfilData = {
        vendor: { name: productData.vendor },

        name: variantTitle,
        description: productData.title + ' - ' + variantTitle,
        code: variant.inventoryItem?.sku,
        list_price: variant.price,
        cost_price: variant.price,
      }

      await queue.dispatch(
        CreateFulfilProductJob,
        { fulfilProductData: fulfilData },
        { attempts: 1 },
      )
    }
  }

  async deleteProduct(id: string) {
    const product = await ZnProduct.query()
      .where({ id })
      .preload('options', (optionQuery) => {
        optionQuery
          .preload('productOptionValues')
          .preload('variantOptionValues')
      })
      .preload('variants', (variantQuery) => {
        variantQuery.preload('image')
      })
      .first()

    if (product) {
      await this.shopifyService.deleteProduct(product.shopifyProductId)

      await product.softDelete()
      for (const option of product.options) {
        for (const value of option.productOptionValues) {
          await value.softDelete()
        }

        for (const value of option.variantOptionValues) {
          await value.softDelete()
        }

        await option.softDelete()
      }

      for (const variant of product.variants) {
        await variant.softDelete()
        await variant.image?.softDelete()
      }
    }
  }

  async changeInventory(productId: string, shopifyLocationId: string) {
    const product = await ZnProduct.find(productId)

    if (!product) { return }

    const shopifyProduct = await this.shopifyService.fetchShopifyProduct(product.shopifyProductId)

    if (!shopifyProduct.product) { return }

    const results = []
    for (const variantNode of shopifyProduct.product.variants.edges) {
      // get the first inventory level/location
      // only work if product has a single location (for vendors)
      const firstInventoryLevel = variantNode.node.inventoryItem.inventoryLevels.nodes[0]

      // in case moving to same location
      if (firstInventoryLevel.location.id == shopifyLocationId) { continue }

      // get only available
      const availableQuantity = firstInventoryLevel.quantities
        .find((quantity: any) => quantity.name == 'available')?.quantity || 0

      const activate = await this.shopifyService.activateInventory(
        variantNode.node.inventoryItem.id,
        shopifyLocationId,
        availableQuantity,
      )

      const deactivate = await this.shopifyService.deactivateInventory(firstInventoryLevel.id)

      results.push({ activate, deactivate })
    }

    return results
  }

  private async saveImagesToDB(
    productId: string,
    shopifyImages: { src: string, position: number }[],
    shopifyVariants: {
      file?: { originalSource: string },
      mediaSrc?: string[],
    }[]
  ) {
    const dbImages = []
    // save product images
    for (const image of shopifyImages) {
      const dbImage = await ZnProductImage.create({
        productId: productId,
        src: image.src,
        position: image.position,
      })

      dbImages.push(dbImage)
    }

    const dbVariants = await ZnProductVariant.query()
      .where({ productId })
      .orderBy('position', 'asc')

    // shopifyVariants are the variants uploaded to shopify
    for (let variantIdx = 0; variantIdx < shopifyVariants.length; variantIdx++) {
      // find the variant image associated with product images
      const variantImage = dbImages.find(img =>
        img.src == shopifyVariants[variantIdx].file?.originalSource || shopifyVariants[variantIdx].mediaSrc?.[0]
      )

      if (variantImage) {
        const currentVariant = dbVariants[variantIdx]
        currentVariant.imageId = variantImage.id
        await currentVariant.save()

        variantImage.variantId = currentVariant.id
        await variantImage.save()
      }
    }

  }

  private transformDataToShopifyProductInput(payload: any, mode?: 'create' | 'update') {
    const productData = {
      title: payload.title,
      descriptionHtml: payload.description,

      vendor: payload.vendor?.companyName,
      productType: payload.productType?.name,
      tags: payload.tags?.map((tag: any) => tag.name),
      category: payload.category?.shopifyId,

      status: payload.status?.toUpperCase(),
    }

    const optionsData = payload.options?.map((prodOp: any) => ({
      name: prodOp.name,
      position: prodOp.position,
      values: prodOp.productOptionValues?.map((prodOpVal: any) => ({ name: prodOpVal.value }))
    }))

    const mediaData = payload.images?.map((image: any) => {
      let data = {}

      if (mode == 'create') {
        data = {
          originalSource: image.src,
          mediaContentType: 'IMAGE',
        }
      } else if (mode == 'update') {
        data = {
          originalSource: image.src,
          contentType: 'IMAGE',
        }
      }

      return data
    })

    const variantsData = payload.variants?.map((vari: any) => {
      let measurement

      // add measurement if weight is a number and weight unit exists
      if (!isNaN(+vari.weight) && vari.weightUnit) {
        const shopifyWeightUnit: any = {
          'g': 'GRAMS',
          'kg': "KILOGRAMS",
          'lb': "POUNDS",
          'oz': "OUNCES",
        }

        measurement = {
          weight: {
            value: isNaN(+vari.weight)
              ? Number.parseFloat(vari.weight)
              : +vari.weight,
            unit: shopifyWeightUnit[vari.weightUnit],
          }
        }
      }

      const inventoryQuantities = []
      const locationId = payload.vendor?.warehouse?.shopifyLocationId
      if (mode == 'create') {
        inventoryQuantities.push({
          locationId: locationId,
          availableQuantity: (isNaN(+vari.inventoryQuantity)
            ? Number.parseInt(vari.inventoryQuantity)
            : +vari.inventoryQuantity)
            || 0,
        })
      } else if (mode == 'update') {
        inventoryQuantities.push({
          locationId: locationId,
          // on_hand or available
          name: 'available',
          quantity: (isNaN(+vari.inventoryQuantity)
            ? Number.parseInt(vari.inventoryQuantity)
            : +vari.inventoryQuantity)
            || 0,
        })
      }

      let mediaSrc
      if (mode == 'create' && vari.image?.src) {
        mediaSrc = vari.image?.src
      }

      let file
      if (mode == 'update' && vari.image?.src) {
        file = {
          originalSource: vari.image?.src,
        }
      }


      return {
        price: vari.price,
        compareAtPrice: vari.compareAtPrice,

        barcode: vari.barcode,
        inventoryItem: {
          sku: vari.sku,
          measurement: measurement,
          requiresShipping: !!measurement,
        },

        inventoryPolicy: vari.inventoryPolicy?.toUpperCase(),
        inventoryQuantities: inventoryQuantities,

        optionValues: vari.optionValues.map((opVal: any) => ({
          name: opVal.value,
          optionName: opVal.option?.name
        })),

        file,
        mediaSrc,
      }
    })

    return { productData, optionsData, mediaData, variantsData }
  }


}
