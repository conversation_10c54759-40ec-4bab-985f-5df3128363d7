import { TransactionSource, TransactionStatus } from '#constants/transaction'
import ZnTransaction from '#models/zn_transaction'
import { PaymentHandleService } from '#services/payment/payment_handle_service'
import logger from '@adonisjs/core/services/logger'

export class TransactionService {
  private paymentHandleService = new PaymentHandleService()

  /**
   * Get all transactions with pagination and filtering
   */
  async getAllTransactions(
    page: number = 1,
    limit: number = 10,
    filters: {
      source?: string
      status?: string
      orderId?: string
      payerEmail?: string
    } = {}
  ) {
    const query = ZnTransaction.query().preload('order').orderBy('createdAt', 'desc')

    // Apply filters
    if (filters.source) {
      query.where('source', filters.source)
    }

    if (filters.status) {
      query.where('status', filters.status)
    }

    if (filters.orderId) {
      query.where('orderId', filters.orderId)
    }

    if (filters.payerEmail) {
      query.where('payerEmail', 'like', `%${filters.payerEmail}%`)
    }

    return query.paginate(page, limit)
  }

  /**
   * Get transaction by ID
   */
  async getTransactionById(id: string): Promise<ZnTransaction> {
    const transaction = await ZnTransaction.query().where('id', id).preload('order').firstOrFail()

    return transaction
  }

  /**
   * Get transactions by order ID
   */
  async getTransactionsByOrderId(orderId: string): Promise<ZnTransaction[]> {
    const transactions = await ZnTransaction.query()
      .where('orderId', orderId)
      .preload('order')
      .orderBy('createdAt', 'desc')

    return transactions
  }

  /**
   * Process refund for a transaction
   */
  async processRefund(transactionId: string): Promise<any> {
    console.log('TransactionService.processRefund called with ID:', transactionId)

    try {
      const transaction = await this.getTransactionById(transactionId)
      console.log('Transaction found:', {
        id: transaction.id,
        status: transaction.status,
        orderId: transaction.orderId,
        amount: transaction.amount,
      })

      if (!transaction.orderId) {
        console.log('Error: Transaction is not associated with an order')
        throw new Error('Transaction is not associated with an order')
      }

      if (transaction.status === TransactionStatus.REFUNDED) {
        console.log('Error: Transaction has already been refunded')
        throw new Error('Transaction has already been refunded')
      }

      if (
        transaction.status !== TransactionStatus.COMPLETED &&
        transaction.status !== TransactionStatus.CAPTURED
      ) {
        console.log('Error: Transaction is not in a refundable state. Status:', transaction.status)
        throw new Error('Transaction is not in a refundable state')
      }

      console.log('Calling paymentHandleService.refundPayment with orderId:', transaction.orderId)

      try {
        // Use existing refund service
        const refundResult = await this.paymentHandleService.refundPayment(transaction.orderId)
        console.log('Refund result received:', refundResult)

        logger.info('Refund processed successfully', {
          transactionId,
          orderId: transaction.orderId,
          result: refundResult,
        })

        return refundResult
      } catch (error) {
        console.error('Error in paymentHandleService.refundPayment:', {
          message: error.message,
          stack: error.stack,
          name: error.name,
        })
        logger.error('Refund processing failed', {
          transactionId,
          orderId: transaction.orderId,
          error: error.message,
        })
        throw error
      }
    } catch (error) {
      console.error('Error in TransactionService.processRefund:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })
      throw error
    }
  }

  /**
   * Get transaction statistics
   */
  async getTransactionStats() {
    const totalTransactions = await ZnTransaction.query().count('* as total')
    const completedTransactions = await ZnTransaction.query()
      .where('status', TransactionStatus.COMPLETED)
      .count('* as completed')
    const refundedTransactions = await ZnTransaction.query()
      .where('status', TransactionStatus.REFUNDED)
      .count('* as refunded')

    return {
      total: totalTransactions[0].$extras.total,
      completed: completedTransactions[0].$extras.completed,
      refunded: refundedTransactions[0].$extras.refunded,
    }
  }

  /**
   * Get transactions by source
   */
  async getTransactionsBySource(source: TransactionSource, page: number = 1, limit: number = 10) {
    const query = ZnTransaction.query()
      .where('source', source)
      .preload('order')
      .orderBy('createdAt', 'desc')

    return query.paginate(page, limit)
  }

  /**
   * Get transactions by status
   */
  async getTransactionsByStatus(status: TransactionStatus, page: number = 1, limit: number = 10) {
    const query = ZnTransaction.query()
      .where('status', status)
      .preload('order')
      .orderBy('createdAt', 'desc')

    return query.paginate(page, limit)
  }
}
