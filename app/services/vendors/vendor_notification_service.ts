import AppMail from "#mails/app_mail";
import VendorOrderReceivedNotification from "#mails/orders/vendor_order_received_notification";
import VendorEarningApprovedNotification from "#mails/vendors/vendor_earning_approved_notification";
import VendorPaymentNotification from "#mails/vendors/vendor_payment_notification";
import VendorRegistrationApprovedNotification from "#mails/vendors/vendor_registration_approved_notification";
import VendorRegistrationConfirmNotification from "#mails/vendors/vendor_registration_confirm_notification";
import VendorRegistrationRejectedNotification from "#mails/vendors/vendor_registration_rejected_notification";
import ZnVendor from "#models/zn_vendor";
import ZnVendorEarning from "#models/zn_vendor_earning";
import ZnVendorOrder from "#models/zn_vendor_order";
import ZnVendorPayment from "#models/zn_vendor_payment";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";
import {ACTION, RESOURCE} from "#constants/authorization";
import {AdminNotificationService} from "../../../admin/services/notification/admin_notification_service.js";
import VendorRegistrationNotification from "#mails/vendors/vendor_registration_notification";

export default class VendorNotificationService {
  private adminNotificationService
  constructor() {
    this.adminNotificationService = new AdminNotificationService()
  }
  async sendRegistrationConfirmationNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationConfirmNotification(vendor.companyName, vendor.email), vendor.companyName);
    //Send email notification to admins
    const admins = await this.adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.VENDOR }
    ])

    for (const admin of admins) {
      await this.sendEmail(new VendorRegistrationNotification(vendor, admin?.name || 'Admin', admin.username), admin.username)
    }
  }

  async sendRegistrationApprovedNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationApprovedNotification(vendor), vendor.companyName);
  }

  async sendRegistrationRejectedNotification(vendor: ZnVendor) {
    await this.sendEmail(new VendorRegistrationRejectedNotification(vendor.companyName, vendor.email, vendor.rejectionReason ?? ''), vendor.companyName);
  }

  async sendEarningApprovedNotification(earning: ZnVendorEarning) {
    const vendorOrder = await ZnVendorOrder.query()
      .where('vendorId', earning.vendor.id)
      .where('orderId', earning.order.id)
      .preload('orderDetails')
      .firstOrFail();
    await this.sendEmail(new VendorEarningApprovedNotification(earning, vendorOrder), earning.vendor.companyName);
  }

  async sendPaymentNotification(payment: ZnVendorPayment) {
    await payment.load('vendor');
    await payment.load('paymentMethod');
    await this.sendEmail(new VendorPaymentNotification(payment), payment.vendor.companyName);
  }

  async sendNewVendorOrderReceivedNotification(vendorOrderId: string) {
    const vendorOrder = await ZnVendorOrder.query()
      .where('id', vendorOrderId)
      .preload('vendor')
      .preload('order', (query) => {
        query
          .preload('user')
          .preload('shippingAddress')
      })
      .preload('orderDetails')
      .firstOrFail();
    if (vendorOrder.vendor.email) {
      await this.sendEmail(new VendorOrderReceivedNotification(vendorOrder), vendorOrder.vendor.companyName);
    }
  }

  private async sendEmail(emailHandler: AppMail, companyName: string) {
    await mail
      .send(emailHandler)
      .then(() => {
        logger.info(`${Object.getPrototypeOf(emailHandler).constructor.name} has been sent successfully to ${companyName}`);
      })
      .catch((error) => {
        console.error('Error when sending email', error);
      })
  }
}
