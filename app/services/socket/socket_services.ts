import ws from '#services/socket/ws'

export class SocketService {
    private namespaceVar: string = ''

    namespace(name: string) {
        this.namespaceVar = name

        return this
    }

    event(data: any) {
        const io = ws.io

        if (!io) {
            return console.log('IO Server not found');
        }

        const eventData = {
            data,

            timestamp: new Date(),
        }

        if (this.namespaceVar) {
            io.of(this.namespaceVar)
                .emit('event', eventData)
        } else {
            io.emit('event', eventData)
        }
    }
}
