import env from '#start/env'
import { Server } from 'socket.io'
// import server from '@adonisjs/core/services/server'

class Ws {
  io: Server | undefined
  private booted = false

  boot() {
    /**
     * Ignore multiple calls to the boot method
     */
    if (this.booted) {
      return
    }

    // Check if socket is enabled in env
    const socketEnabled = env.get('SOCKET_ENABLED') === 'true'
    if (!socketEnabled) {
      console.log('Socket server is disabled')
      return
    }

    this.booted = true

    const socketPort = Number(env.get('SOCKET_PORT'))

    let socketOrigins: string[] = []
    const envOrigins = env.get("SOCKET_CORS_ORIGINS")
    if (envOrigins) {
      socketOrigins = envOrigins.split(',')
        .map(origin => {

          // remove trailling slashes
          let newOrigin = origin
          if (origin.endsWith('/')) {
            newOrigin = origin.slice(0, -1)
          }

          return newOrigin
        })
    }

    this.io = new Server(socketPort, {
      cors: {
        // all origin if 
        origin: envOrigins ? socketOrigins : '*',
      },
    })

    this.io.use((socket, next) => {
      const socketPassword = env.get('SOCKET_PASSWORD')

      if (socket.handshake.auth.token && socket.handshake.auth.token == socketPassword) {
        next()
      } else {
        next(new Error('Invalid Socket Password'))
      }
    })

    this.io.on('connection', (socket) => {
      console.log('Socket []', 'Client connected:', socket.id)

      // // Test event
      // socket.on('message', (data) => {
      //   console.log('Message received:', data)
      //   socket.emit('response', { success: true, data })
      // })

      //   // receive then emit
      //   socket.on('message', (data) => {
      //     console.log('message received', data);

      //     socket.broadcast.emit('broadcast emit', data)
      //     socket.emit('emit', data)
      //   })

      socket.on('disconnect', () => {
        console.log('Socket []', 'Client disconnected:', socket.id)
      })
    })

    
    this.io.of('chat-rooms').use((socket, next) => {
      const socketPassword = env.get('SOCKET_PASSWORD')

      if (socket.handshake.auth.token && socket.handshake.auth.token == socketPassword) {
        next()
      } else {
        next(new Error('Invalid Socket Password'))
      }
    })

    this.io.of('chat-rooms').on('connection', (socket) => {
      console.log('Socket [chat-rooms]', 'Client connected:', socket.id)

      socket.on('disconnect', () => {
        console.log('Socket [chat-rooms]', 'Client disconnected:', socket.id)
      })
    })


    console.log(`Socket server started on port ${socketPort}`)
  }
}

export default new Ws()
