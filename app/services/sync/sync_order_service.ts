import ZnAddress from '#models/zn_address'
import ZnCartItem from '#models/zn_cart_item'
import ZnCartSection from '#models/zn_cart_section'
import ZnOrder from '#models/zn_order'
import ZnOrderDetail from '#models/zn_order_detail'
import ZnOrderDiscount from '#models/zn_order_discount'
import ZnOrderFulfillment from '#models/zn_order_fulfillment'
import ZnProductVariant from '#models/zn_product_variant'
import ZnUser from '#models/zn_user'
import { GiftService } from '#services/shop/gift_service'
import logger from '@adonisjs/core/services/logger'
import { DateTime } from 'luxon'
import {EOrderStatus} from "#constants/order";

export class SyncOrderService {
  public async syncOrderFromShopify(order: any) {
    const email = order?.customer?.email || null

    let user = null
    if (email) {
      user = await ZnUser.findBy({ email: email })
      if (!user) {
        // Save customer
        user = await ZnUser.updateOrCreate(
          { email },
          {
            firstName: order.customer?.firstName || 'Guest',
            lastName: order.customer?.lastName || '',
            email: email,
            shopifyCustomerId: order.customer?.legacyResourceId || null,
            password: '', // Default password or logic to handle securely
          }
        )
      }
    }

    // Save shipping address
    const shippingAddress = order.shippingAddress || {}
    const shipping = await this.syncAddress(shippingAddress)

    // Save billing address
    const billingAddress = order.billingAddress || {}
    const billing = await this.syncAddress(billingAddress)
    // Save order
    const dbOrder = await ZnOrder.updateOrCreate(
      { shopifyId: order.id },
      {
        shopifyId: order.id,
        name: order.name,
        email: email,
        status: order.cancelledAt !== null ? EOrderStatus.Cancelled : '',
        financialStatus: order.displayFinancialStatus?.toLowerCase() || null,
        fulfilledAt:
          order.displayFulfillmentStatus?.toLowerCase() === 'fulfilled'
            ? (DateTime.fromJSDate(new Date(order.createdAt)).toFormat(
              'yyyy-MM-dd HH:mm:ss'
            ) as any)
            : null,

        currentTotalPrice: parseFloat(order?.currentTotalPriceSet?.shopMoney?.amount || '0'),
        currentSubtotalPrice: parseFloat(order.currentSubtotalPriceSet?.shopMoney?.amount || '0'),
        currentTotalDiscounts: parseFloat(order.currentTotalDiscountsSet?.shopMoney?.amount || '0'),
        currentTotalTax: parseFloat(order.currentTotalTaxSet?.shopMoney?.amount || '0'),

        totalPrice: parseFloat(order?.totalPriceSet?.shopMoney?.amount || '0'),
        subtotalPrice: parseFloat(order?.subtotalPriceSet?.shopMoney?.amount || '0'),
        totalDiscounts: parseFloat(order?.totalDiscountsSet?.shopMoney?.amount || '0'),
        totalTax: parseFloat(order?.totalTaxSet?.shopMoney?.amount || '0'),
        totalShipping: parseFloat(order?.totalShippingPriceSet?.shopMoney?.amount || '0'),

        currency: order?.totalPriceSet?.shopMoney?.currencyCode?.toLowerCase() || 'usd',
        customerId: order.customer?.id || null,
        note: order.note || null,
        createdAt: DateTime.fromJSDate(new Date(order.createdAt)).toFormat(
          'yyyy-MM-dd HH:mm:ss'
        ) as any,
        cancelledAt: order.cancelledAt
          ? (DateTime.fromJSDate(new Date(order.cancelledAt)).toFormat(
            'yyyy-MM-dd HH:mm:ss'
          ) as any)
          : null,
        closedAt: order.closedAt
          ? (DateTime.fromJSDate(new Date(order.closedAt)).toFormat('yyyy-MM-dd HH:mm:ss') as any)
          : null,
        userId: user?.id || null,
        shippingId: shipping?.id,
        billingId: billing?.id,
      }
    )

    // Associate addresses with order
    // if (dbOrder && shipping) await dbOrder.related('shipping').associate(shipping);
    // if (dbOrder && billing) await dbOrder.related('billing').associate(billing);

    // Get giftIds
    const giftId = order?.customAttributes?.find((item: any) => item.key === 'giftId')?.value
    let giftIds = []
    if (giftId) {
      giftIds.push(giftId)
    }

    // Save order details (line items)
    const newOrderDetailIds: string[] = []
    for (const item of order.lineItems.edges || []) {
      const lineItem = item.node
      //Get giftId
      const _gift = lineItem?.customAttributes?.find((item: any) => item.key === 'giftId')?.value
      if (_gift) {
        giftIds.push(_gift)
      }
      if (lineItem.variant?.sku) {
        const variant = await ZnProductVariant.findBy({ sku: lineItem.variant?.sku })
        if (variant) {
          const detail = await ZnOrderDetail.updateOrCreate(
            { shopifyId: lineItem.id },
            {
              orderId: dbOrder.id,
              shopifyId: lineItem.id,
              title: lineItem.title || '',
              quantity: lineItem.quantity || 0,
              currentQuantity: lineItem.currentQuantity || 0,
              variantId: variant?.id, // Save variant ID
              sku: lineItem.variant?.sku, // Save variant SKU
              price: Number(lineItem?.originalUnitPriceSet?.shopMoney?.amount || '0'),
              discount: Number(lineItem?.totalDiscountSet?.shopMoney?.amount || '0'),
            }
          )
          newOrderDetailIds.push(detail.id)
        }
      }
    }

    // Delete order details (line items) that are not in shopify order lines
    const dbOrderDetailsNotInShopify = await ZnOrderDetail.query()
      .where({ orderId: dbOrder.id })
      .whereNotIn('id', newOrderDetailIds)
    for (const detail of dbOrderDetailsNotInShopify) {
      await detail.softDelete()
    }

    // Remove discounts of the order
    await ZnOrderDiscount.query().where('orderId', dbOrder.id).delete()

    for (const item of order?.discountApplications?.nodes || []) {
      if (item.targetSelection.toLowerCase() === 'all') {
        await ZnOrderDiscount.create({
          orderId: dbOrder.id,
          targetType: item.targetType,
          amount: parseFloat(item.value.amount || 0),
          percentage: parseFloat(item.value.percentage || 0),
        })
      }
    }

    // Use gift
    if (user?.id) {
      // Check if the order has a gift ID in note attributes
      giftIds.map(async (giftId) => {
        if (giftId) {
          const giftService = new GiftService()
          await giftService.useGift({
            userId: user.id,
            giftId,
            orderId: dbOrder.id,
          })
        }
      })
    }

    // get order details from order for faster loading
    await dbOrder.load('orderDetails')
    const dbOrderDetails = dbOrder.orderDetails
    // sync fulfillments
    for (const fulfillment of order.fulfillments) {
      // remove duplicated fulfillments except 1
      const dbFulfillments = await ZnOrderFulfillment.query()
        .where({ shopifyFulfillmentId: fulfillment.id })

      for (const dbFulfillment of dbFulfillments.slice(1)) {
        await dbFulfillment.delete()
      }

      // update fulfillment
      const dbFulfillment = await ZnOrderFulfillment.updateOrCreate(
        { shopifyFulfillmentId: fulfillment.id },
        {
          shopifyFulfillmentId: fulfillment.id,
          orderId: dbOrder.id,
          status: fulfillment.status.toLowerCase(),
          trackingCompany: fulfillment.trackingInfo[0]?.company,
          trackingNumber: fulfillment.trackingInfo[0]?.number,
          trackingUrl: fulfillment.trackingInfo[0]?.url,
          trackingNumbers: fulfillment.trackingInfo
            .map((track: any) => track.number)
            .join(','),
          trackingUrls: fulfillment.trackingInfo
            .map((track: any) => track.url)
            .join(','),
        }
      )

      // update fulfillment and order details links
      const fulfillmentLineItems = fulfillment.fulfillmentLineItems.nodes

      const fulfillmentOrderDetails: any = {}
      for (const item of fulfillmentLineItems) {
        const dbDetails = dbOrderDetails.find((details) => details.shopifyId == item.lineItem?.id)

        if (dbDetails) {
          fulfillmentOrderDetails[dbDetails.id] = {
            // id: randomUUID(),
            quantity: item.quantity
          }
        }
      }

      await dbFulfillment.related('orderDetails').detach()
      await dbFulfillment.related('orderDetails').sync(fulfillmentOrderDetails)

    }

    return dbOrder
  }

  private async syncAddress(address: any) {
    const addressId = address.id?.split('?')?.at(0)?.split('/')?.pop()
    if (!addressId) {
      return
    }
    const zurnoAddress = await ZnAddress.updateOrCreate(
      { shopifyId: addressId },
      {
        shopifyId: addressId,
        firstName: address.firstName,
        lastName: address.lastName,
        address1: address.address1,
        address2: address.address2,
        city: address.city,
        province: address.province,
        country: address.country,
        provinceCode: address.provinceCode,
        countryCode: address.countryCode,
        phone: address.phone,
        zip: address.zip,
      }
    )

    return zurnoAddress
  }

  public async syncOrderFromShopifyWebhook(order: any) {
    const email = order?.email || order?.customer?.email || null

    let user = null
    if (email) {
      user = await ZnUser.findBy({ email: email })
      if (!user) {
        // Save customer
        user = await ZnUser.updateOrCreate(
          { email },
          {
            firstName: order.customer?.first_name || 'Guest',
            lastName: order.customer?.last_name || '',
            email: email,
            shopifyCustomerId: order.customer?.id || null,
            phone: order.customer?.phone || null,
            password: '', // Default password or logic to handle securely
          }
        )
      }
    }

    // Save shipping address
    const shippingAddress = order.shipping_address || null
    const shipping = shippingAddress
      ? await ZnAddress.updateOrCreate(
        { address1: shippingAddress?.address1, zip: shippingAddress?.zip },
        {
          address1: shippingAddress.address1,
          address2: shippingAddress.address2 || null,
          city: shippingAddress.city,
          province: shippingAddress.province,
          provinceCode: shippingAddress.province_code,
          country: shippingAddress.country,
          countryCode: shippingAddress.country_code,
          zip: shippingAddress.zip,
          phone: shippingAddress.phone || null,
          latitude: shippingAddress.latitude || null,
          longitude: shippingAddress.longitude || null,
          firstName: shippingAddress.first_name || null,
          lastName: shippingAddress.last_name || null,
        }
      )
      : null

    // Save billing address
    const billingAddress = order.billing_address || null
    const billing = billingAddress
      ? await ZnAddress.updateOrCreate(
        { address1: billingAddress?.address1, zip: billingAddress?.zip },
        {
          address1: billingAddress.address1,
          address2: billingAddress.address2 || null,
          city: billingAddress.city,
          province: billingAddress.province,
          provinceCode: billingAddress.province_code,
          country: billingAddress.country,
          countryCode: billingAddress.country_code,
          zip: billingAddress.zip,
          phone: billingAddress.phone || null,
          latitude: billingAddress.latitude || null,
          longitude: billingAddress.longitude || null,
          firstName: billingAddress.first_name || null,
          lastName: billingAddress.last_name || null,
        }
      )
      : null

    // Save order
    const orderId = `gid://shopify/Order/${order.id}`
    let status = EOrderStatus.Processing
    if (order.cancelled_at !== null) {
      status = EOrderStatus.Cancelled
    } else if (order.source_name === 'pos' && order.shipping_lines?.some((line: any) => line.code === 'pickup')) {
      status = EOrderStatus.Completed
    }

    const dbOrder = await ZnOrder.updateOrCreate(
      { shopifyId: orderId },
      {
        shopifyId: orderId,
        name: order.name,
        email: email,
        status: status,
        financialStatus: order.financial_status?.toLowerCase() || null,

        currentTotalPrice: parseFloat(order.current_total_price || '0'),
        currentSubtotalPrice: parseFloat(order.current_subtotal_price || '0'),
        currentTotalTax: parseFloat(order.current_total_tax || '0'),
        currentTotalDiscounts: parseFloat(order.current_total_discounts || '0'),

        totalPrice: parseFloat(order.total_price || '0'),
        subtotalPrice: parseFloat(order.subtotal_price || '0'),
        totalTax: parseFloat(order.total_tax || '0'),
        totalDiscounts: parseFloat(order.total_discounts || '0'),

        totalShipping: parseFloat(
          order.shipping_lines?.reduce(
            (acc: any, line: any) => acc + parseFloat(line.price || '0'),
            0
          ) || '0'
        ),
        currency: order.currency?.toLowerCase() || 'usd',
        customerId: order.customer?.id || null,
        createdAt: DateTime.fromISO(order.created_at).toFormat('yyyy-MM-dd HH:mm:ss') as any,
        updatedAt: DateTime.fromISO(order.updated_at).toFormat('yyyy-MM-dd HH:mm:ss') as any,
        cancelledAt: order.cancelled_at
          ? (DateTime.fromISO(order.cancelled_at).toFormat('yyyy-MM-dd HH:mm:ss') as any)
          : null,
        userId: user?.id || null,
        shippingId: shipping?.id,
        billingId: billing?.id,
        note: order.note || null,
      }
    )

    // Remove CartSection
    let cartSectionIds = order?.note_attributes?.find(
      (item: any) => item.name === 'cartSectionIds'
    )?.value
    if (cartSectionIds) {
      cartSectionIds = cartSectionIds.split(',').filter(Boolean)
      await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      await ZnCartSection.query().whereIn('id', cartSectionIds).delete()
    }

    await ZnOrderDiscount.query().where('orderId', dbOrder.id).delete()

    for (const item of order?.discount_applications || []) {
      let amount = 0
      let percentage = 0
      // Check if the discount is a percentage or a fixed amount
      if (item.value_type === 'percentage') {
        percentage = parseFloat(item.value || 0)
      } else {
        amount = parseFloat(item.value || 0)
      }

      await ZnOrderDiscount.create({
        orderId: dbOrder.id,
        targetType: item.target_type,
        amount,
        percentage,
        discountCode: item.code || null,
        discountType: item.type || null,
        description: item.title || null,
        discountDetails: JSON.stringify(item),
      })
    }
    // Save order details (line items)
    let giftIds = [order?.note_attributes?.find((item: any) => item.name === 'giftId')?.value]

    for (const item of order?.line_items || []) {
      const lineItemId = `gid://shopify/LineItem/${item.id}`
      const variant = await ZnProductVariant.findBy({
        shopifyVariantId: `gid://shopify/ProductVariant/${item.variant_id}`,
      })

      //Get giftId
      const _gift = item.custom_attributes?.find((item: any) => item.name === 'giftId')?.value
      if (_gift) {
        giftIds.push(_gift)
      }

      await ZnOrderDetail.updateOrCreate(
        { shopifyId: lineItemId },
        {
          orderId: dbOrder.id,
          shopifyId: lineItemId,
          title: item.title || '',
          quantity: item.quantity || 0,
          currentQuantity: item.current_quantity || 0,
          variantId: variant?.id || null,
          sku: item.sku || '',
          price: parseFloat(item.price || '0'),
          tax: parseFloat(
            item.tax_lines?.reduce((acc: any, tax: any) => acc + parseFloat(tax.price || '0'), 0) ||
            '0'
          ),
          discount: parseFloat(
            item.discount_allocations?.reduce(
              (acc: any, discount: any) => acc + parseFloat(discount.amount || '0'),
              0
            ) || '0'
          ),
        }
      )
    }

    // Use gift
    if (user?.id) {
      // Check if the order has a gift ID in note attributes
      giftIds.map(async (giftId) => {
        if (giftId) {
          const giftService = new GiftService()
          await giftService.useGift({
            userId: user.id,
            giftId,
            orderId: dbOrder.id,
          })
        }
      })
    }

    return dbOrder
  }

  async isOrderCreating(updatingOrder: ZnOrder | null) {
    const doesOrderExist = updatingOrder !== null && updatingOrder !== undefined
    logger.debug(
      'SyncOrderService.isOrderCreating:%s (%s)',
      !doesOrderExist,
      doesOrderExist ? 'ZnOrder was already created' : 'ZnOrder is not created yet'
    )
    return !doesOrderExist
  }

  async isOrderCancelling(updatingOrder: ZnOrder | null, order: any) {
    if (updatingOrder === null || updatingOrder === undefined) {
      logger.debug('SyncOrderService.isOrderCancelling:false (ZnOrder is not created yet)')
      return false
    }

    if (updatingOrder.status === 'cancel') {
      logger.debug('SyncOrderService.isOrderCancelling:false (ZnOrder was already cancelled)')
      return false
    }

    if (
      order.cancelled_at === null ||
      order.cancelled_at === undefined ||
      order.cancelled_at.length === 0
    ) {
      logger.debug('SyncOrderService.isOrderCancelling:false (Shopify order is not cancelled)')
      return false
    }

    logger.debug(
      'SyncOrderService.isOrderCancelling:true (Shopify order was cancelled at %s)',
      order.cancelled_at
    )
    return true
  }

  async isOrderRefunding(updatingOrder: ZnOrder | null, order: any) {
    if (updatingOrder === null || updatingOrder === undefined) {
      logger.debug('SyncOrderService.isOrderRefunding:false (ZnOrder is not created yet)')
      return false
    }

    if (updatingOrder.financialStatus === 'refunded') {
      logger.debug('SyncOrderService.isOrderRefunding:false (ZnOrder was already refunded)')
      return false
    }

    if (order.financial_status !== 'partially_refunded' && order.financial_status !== 'refunded') {
      logger.debug(
        'SyncOrderService.isOrderRefunding:false (payload.financial_status: %s)',
        order.financial_status
      )
      return false
    }

    if (
      updatingOrder.financialStatus === 'partially_refunded' &&
      order.financial_status === 'partially_refunded'
    ) {
      const isTotalPriceUpdated =
        updatingOrder.currentTotalPrice !== parseFloat(order.current_total_price)
      logger.debug(
        'SyncOrderService.isOrderRefunding:%s (%s)',
        isTotalPriceUpdated,
        isTotalPriceUpdated
          ? 'Shopify order has updated new total price'
          : "Shopify order doesn't update total price"
      )
      return isTotalPriceUpdated
    }

    const result = updatingOrder.financialStatus !== order.financial_status
    logger.debug(
      'SyncOrderService.isOrderRefunding:%s (Updating finalcial status: %s -> %s)',
      result,
      updatingOrder.financialStatus,
      order.financial_status
    )
    return result
  }
}
