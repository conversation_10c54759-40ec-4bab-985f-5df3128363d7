import { EAffiliateSnapshot<PERSON>ey } from "#constants/affiliate_snapshot_enum";
import { ECommissionGroupType } from "#constants/commission_group_type";
import ZnAffiliate from "#models/zn_affiliate";
import ZnAffiliateSnapshot from "#models/zn_affiliate_snapshot";
import { format } from "date-fns";
import { CommissionRateRange } from "./affiliation_commission_rate_service.js";
import logger from "@adonisjs/core/services/logger";

export class AffiliationSnapshotsService {
  async getAllSnapshots(affiliateId: string, groupType: ECommissionGroupType, date?: Date): Promise<any> {
    const monthString = format(date ?? new Date(), 'yyyy-MM');
    const totalSales = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${EAffiliateSnapshotKey.TOTAL_SALES}`);
    const commissionRate = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RATE}`);
    const commissionRangeFrom = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_FROM}`);
    const commissionRangeTo = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_TO}`);
    const pendingCommissionAmount = await this.getSnapshotAsNumber(affiliateId, EAffiliateSnapshotKey.PENDING_COMMISSION_AMOUNT);
    const referredCustomersCount = await this.getSnapshotAsNumber(affiliateId, EAffiliateSnapshotKey.REFERRED_CUSTOMERS_COUNT);

    return {
      totalSales,
      commissionRate,
      commissionRangeFrom,
      commissionRangeTo,
      pendingCommissionAmount,
      referredCustomersCount
    }
  }

  async getSnapshotAsString(affiliateId: string, key: string): Promise<string> {
    const snapshot = await ZnAffiliateSnapshot.query()
      .where('affiliateId', affiliateId)
      .where('key', key)
      .first();

    return snapshot ? snapshot.value : '';
  }

  async getSnapshotAsNumber(affiliateId: string, key: string): Promise<number> {
    const snapshotValue = await this.getSnapshotAsString(affiliateId, key);
    if (snapshotValue.trim() !== "" && !Number.isNaN(snapshotValue)) {
      return parseFloat(snapshotValue);
    }
    return 0;
  }

  async setSnapshot(affiliateId: string, key: string, value: string) {
    const affiliate = await ZnAffiliate.find(affiliateId);
    if (!affiliate) {
      logger.warn(`[setSnapshot] Cannot find affiliate with ID ${affiliateId}`);
      return;
    }

    return await ZnAffiliateSnapshot.updateOrCreate({
      affiliateId,
      key
    }, {
      affiliateId,
      key,
      value
    });
  }

  async setTotalSalesSnapshot(affiliateId: string, month: String, totalSales: number) {
    return await this.setSnapshot(affiliateId, `${month}_${EAffiliateSnapshotKey.TOTAL_SALES}`, totalSales.toString());
  }

  async updateCommissionRateSnapshot(affiliateId: string, month: String, groupType: ECommissionGroupType, range: CommissionRateRange) {
    await Promise.all([
      this.setSnapshot(affiliateId, `${month}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RATE}`, range.rate.toString()),
      this.setSnapshot(affiliateId, `${month}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_FROM}`, range.revenueFrom.toString()),
      this.setSnapshot(affiliateId, `${month}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_TO}`, range.revenueTo.toString())
    ]);
  }
}