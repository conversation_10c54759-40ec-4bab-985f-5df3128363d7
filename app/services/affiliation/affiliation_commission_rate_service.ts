import { ECommissionGroupType } from "#constants/commission_group_type";
import ZnAffiliateCommissionRate from "#models/zn_affiliate_commission_rate";
import ZnAffiliateTierCommissionGroup from "#models/zn_affiliate_tier_commission_group";
import logger from "@adonisjs/core/services/logger";

export interface CommissionRateRange {
  revenueFrom: number;
  revenueTo: number;
  rate: number;
}

export default class AffiliationCommissionRateService {
  async create(commissionGroupId: string, data: Partial<ZnAffiliateCommissionRate>): Promise<ZnAffiliateCommissionRate> {
    const commissionGroup = await ZnAffiliateTierCommissionGroup.findOrFail(commissionGroupId);

    return await ZnAffiliateCommissionRate.create({
      ...data,
      commissionGroupId: commissionGroup.id
    });
  }

  async findById(id: number): Promise<ZnAffiliateCommissionRate | null> {
    return await ZnAffiliateCommissionRate.findOrFail(id);
  }

  async findAllByCommissionGroupId(commissionGroupId: string): Promise<ZnAffiliateCommissionRate[]> {
    return await ZnAffiliateCommissionRate.query()
      .where('commissionGroupId', commissionGroupId)
      .orderBy('revenueFrom', 'asc');
  }

  async findCommissionRateRanges(affiliateId: string, groupType: ECommissionGroupType, totalSales: number): Promise<CommissionRateRange> {
    const commissionGroup = await ZnAffiliateTierCommissionGroup.query()
      .whereHas('tier', (query) => {
        query.whereHas('affiliates', (query) => {
          query.where('id', affiliateId);
        });
      })
      .where('type', groupType)
      .preload('commissionRates')
      .first();

    if (!commissionGroup) {
      logger.warn(`[findCommissionRateRanges] Cannot find any commission rate range of ${groupType} group of affiliate ID ${affiliateId}`);
      return {
        revenueFrom: -1,
        revenueTo: -1,
        rate: 0,
      };
    }

    if (!commissionGroup.commissionRates || commissionGroup.commissionRates.length === 0) {
      logger.warn(`[findCommissionRateRanges] Commission group ID ${commissionGroup.id} doesn't contain any commission rate range`);
      return {
        revenueFrom: -1,
        revenueTo: -1,
        rate: 0,
      };
    }

    let lowerRate = null;
    let upperRate = null;
    for (const rate of commissionGroup.commissionRates) {
      if (rate.revenueFrom <= totalSales) {
        if (!lowerRate || rate.revenueFrom > lowerRate.revenueFrom) {
          lowerRate = rate;
        }
      }

      if (rate.revenueFrom > totalSales) {
        if (!upperRate || rate.revenueFrom < upperRate.revenueFrom) {
          upperRate = rate;
        }
      }
    }

    const range: CommissionRateRange = {
      revenueFrom: lowerRate?.revenueFrom ?? -1,
      revenueTo: upperRate?.revenueFrom ?? -1,
      rate: lowerRate?.commissionRate ?? 0,
    };
    logger.info(`[findCommissionRateRanges] Commission rate range of ${groupType} group of affiliate ID ${affiliateId}: ${JSON.stringify(range, null, 2)}`);

    return range;
  }

  async findCommissionRateRangesByAffiliateAndTotalSales(affiliateId: string, totalSales: number): Promise<{ title: string; from: number; to: number; rate: number }[]> {
    const commissionGroups = await ZnAffiliateTierCommissionGroup.query()
      .whereHas('tier', (query) => {
        query.whereHas('affiliates', (query) => {
          query.where('id', affiliateId);
        });
      })
      .preload('commissionRates');

    const resultGroups: { title: string; from: number; to: number; rate: number }[] = []
    for (const group of commissionGroups) {
      if (!group.commissionRates || group.commissionRates.length === 0) continue;

      let lowerRate = null;
      let upperRate = null;
      for (const rate of group.commissionRates) {
        if (rate.revenueFrom <= totalSales) {
          if (!lowerRate || rate.revenueFrom > lowerRate.revenueFrom) {
            lowerRate = rate;
          }
        }

        if (rate.revenueFrom > totalSales) {
          if (!upperRate || rate.revenueFrom < upperRate.revenueFrom) {
            upperRate = rate;
          }
        }
      }

      resultGroups.push({
        title: group.title,
        from: lowerRate?.revenueFrom ?? -1,
        to: upperRate?.revenueFrom ?? -1,
        rate: lowerRate?.commissionRate ?? 0,
      });
    }

    return resultGroups;
  }

  async update(id: number, data: Partial<ZnAffiliateCommissionRate>): Promise<ZnAffiliateCommissionRate> {
    const commissionRate = await ZnAffiliateCommissionRate.findOrFail(id);
    commissionRate.merge(data);
    return await commissionRate.save();
  }

  async delete(id: number): Promise<void> {
    const commissionRate = await ZnAffiliateCommissionRate.findOrFail(id);
    await commissionRate.softDelete();
  }
}