import { belongsTo, column } from "@adonisjs/lucid/orm";
import AppModel from "./app_model.js";
import type { BelongsTo } from "@adonisjs/lucid/types/relations";
import ZnAffiliateTierCommissionGroup from "./zn_affiliate_tier_commission_group.js";

export default class ZnAffiliateCommissionRate extends AppModel {
  static table = "zn_affiliate_commission_rates";

  @column({
    columnName: "revenueFrom",
    consume: (value: string) => parseFloat(value)
  })
  declare revenueFrom: number;

  @column({
    columnName: "commissionRate",
    consume: (value: string) => parseFloat(value)
  })
  declare commissionRate: number;

  @column({ columnName: "commissionGroupId" })
  declare commissionGroupId: string;

  @belongsTo(() => ZnAffiliateTierCommissionGroup, {
    foreignKey: "commissionGroupId"
  })
  declare commissionGroup: BelongsTo<typeof ZnAffiliateTierCommissionGroup>;

  // Used to set the upper range later
  @column({ serializeAs: 'revenueTo' })
  public revenueTo?: number;
}