import AppModel from "#models/app_model";
import {belongsTo, column} from "@adonisjs/lucid/orm";
import type { BelongsTo } from "@adonisjs/lucid/types/relations";
import ZnOrderFulfillment from "#models/zn_order_fulfillment";
import {DateTime} from "luxon";

export default class ZnPackageTrackingHistory extends AppModel {
  static table = 'zn_package_tracking_histories'

  @column({ columnName: 'fulfilId' })
  declare fulfilId: string

  @column({ columnName: 'trackingNumber' })
  declare trackingNumber: string

  @column({ columnName: 'carrier' })
  declare carrier: string

  @column({ columnName: 'objectId' })
  declare objectId: string

  @column({ columnName: 'status' })
  declare status: string

  @column({ columnName: 'statusDetails' })
  declare statusDetails: string

  @column.dateTime({ columnName: 'statusDate' })
  declare statusDate: DateTime

  @column.dateTime({ columnName: 'objectCreated' })
  declare objectCreated: DateTime

  @column({ columnName: 'city' })
  declare city: string

  @column({ columnName: 'state' })
  declare state: string

  @column({ columnName: 'zip' })
  declare zip: string

  @column({ columnName: 'country' })
  declare country: string

  @belongsTo(() => ZnOrderFulfillment, {
    foreignKey: 'id',
  })
  declare fulfillment: BelongsTo<typeof ZnOrderFulfillment>
}
