import { belongsTo, column } from "@adonisjs/lucid/orm";
import AppModel from "./app_model.js"
import type { BelongsTo } from "@adonisjs/lucid/types/relations";
import ZnAffiliate from "./zn_affiliate.js";

export default class ZnAffiliateSnapshot extends AppModel {
  static table = "zn_affiliate_snapshots";

  @column({ columnName: "key" })
  declare key: string;

  @column({ columnName: "value" })
  declare value: string;

  @column({ columnName: "affiliateId" })
  declare affiliateId: string;

  @belongsTo(() => ZnAffiliate, {
    foreignKey: 'affiliateId'
  })
  declare affiliate: BelongsTo<typeof ZnAffiliate>
}