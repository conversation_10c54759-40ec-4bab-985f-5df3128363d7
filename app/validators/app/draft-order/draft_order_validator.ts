import vine from '@vinejs/vine'

export const CreateDraftOrderValidator = vine.compile(
  vine.object({
    items: vine.array(
      vine.object({
        variantId: vine.string(),
        quantity: vine.number(),
        cartSectionId: vine.string().optional(),
        fastBundleDiscountId: vine.number().optional(),
        referredAffiliateId: vine.string().optional(),
      })
    ),
    shippingAddressId: vine.string().optional(),
    note: vine.string().optional(),
    os: vine.string().optional(),
    appVersion: vine.string().optional(),
    discountCodes: vine.array(vine.string()).optional(),
    paymentMethod: vine.string().optional(),
  })
)

export const DeleteOrderDetailValidator = vine.compile(
  vine.object({
    removeItemId: vine.string(),
  })
)

export const UpdateHandleDraftOrderValidator = vine.compile(
  vine.object({
    handle: vine.string(),
  })
)

export const UpdateNoteDraftOrderValidator = vine.compile(
  vine.object({
    note: vine.string(),
  })
)

export const UpdateAddressDraftOrderValidator = vine.compile(
  vine.object({
    shippingAddressId: vine.string(),
  })
)

export const UpdateDiscountDraftOrderValidator = vine.compile(
  vine.object({
    discountCodes: vine.array(vine.string()),
  })
)
