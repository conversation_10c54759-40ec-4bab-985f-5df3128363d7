import vine from '@vinejs/vine'

/**
 * Validator for listing transactions with filters
 */
export const listTransactionsValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).optional(),
    source: vine.string().optional(),
    status: vine.string().optional(),
    orderId: vine.string().uuid().optional(),
    payerEmail: vine.string().email().optional(),
  })
)

/**
 * Validator for getting transaction by ID
 */
export const getTransactionValidator = vine.compile(
  vine.object({
    id: vine.string().uuid(),
  })
)

/**
 * Validator for getting transactions by order ID
 */
export const getTransactionsByOrderValidator = vine.compile(
  vine.object({
    orderId: vine.string().uuid(),
  })
)

/**
 * Validator for processing refund
 */
export const processRefundValidator = vine.compile(
  vine.object({
    id: vine.string().uuid(),
  })
)
