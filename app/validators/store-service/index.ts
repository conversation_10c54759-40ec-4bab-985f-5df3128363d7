import vine from '@vinejs/vine'

const stringToNumberTransform = (value: any) => {
  if (value === '' || value === null || value === undefined) return undefined
  const num = Number(value)
  return isNaN(num) ? undefined : num
}

const numericQueryParam = () =>
  vine
    .string()
    .optional()
    .transform(stringToNumberTransform)

export const listServiceValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid(),
    page: numericQueryParam(),
    limit: numericQueryParam(),
    search: vine.string().optional(),
    categories: vine.array(vine.string().uuid()).optional(),
    priceFrom: numericQueryParam(),
    priceTo: numericQueryParam(),
    durationFrom: numericQueryParam(),
    durationTo: numericQueryParam(),
  })
)

export const listCombinedValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid(),
    page: numericQueryParam(),
    limit: numericQueryParam(),
    search: vine.string().optional(),
    categories: vine.array(vine.string().uuid()).optional(),
    priceFrom: numericQueryParam(),
    priceTo: numericQueryParam(),
    durationFrom: numericQueryParam(),
    durationTo: numericQueryParam(),
  })
)

export const createServiceValidator = vine.compile(
  vine.object({
    name: vine.string(),
    price: vine.number(),
    duration: vine.number(),
    storeId: vine.string().uuid(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
    categories: vine.array(vine.string().uuid()).optional(),
  })
)

export const updateServiceValidator = vine.compile(
  vine.object({
    name: vine.string().optional(),
    price: vine.number().optional(),
    duration: vine.number().optional(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
    categories: vine.array(vine.string().uuid()).optional(),
  })
)

// Category Validators
export const listCategoryValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid(),
    page: numericQueryParam(),
    limit: numericQueryParam(),
    search: vine.string().optional(),
    priceFrom: numericQueryParam(),
    priceTo: numericQueryParam(),
    durationFrom: numericQueryParam(),
    durationTo: numericQueryParam(),
  })
)

export const createCategoryValidator = vine.compile(
  vine.object({
    name: vine.string(),
    storeId: vine.string().uuid(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
  })
)

export const updateCategoryValidator = vine.compile(
  vine.object({
    name: vine.string().optional(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
  })
)

// Package Validators
export const listPackageValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid(),
    page: numericQueryParam(),
    limit: numericQueryParam(),
    search: vine.string().optional(),
    priceFrom: numericQueryParam(),
    priceTo: numericQueryParam(),
    durationFrom: numericQueryParam(),
    durationTo: numericQueryParam(),
  })
)

export const createPackageValidator = vine.compile(
  vine.object({
    name: vine.string(),
    storeId: vine.string().uuid(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
    services: vine.array(
      vine.object({
        id: vine.string().uuid(),
        customPrice: vine.number().optional(),
      })
    ),
  })
)

export const updatePackageValidator = vine.compile(
  vine.object({
    name: vine.string().optional(),
    imageId: vine
      .string()
      .uuid()
      .optional()
      .transform((value) => {
        return value === '' || value === null || value === undefined ? null : value
      }),
    services: vine
      .array(
        vine.object({
          id: vine.string().uuid(),
          customPrice: vine.number().optional(),
        })
      )
      .optional(),
  })
)

// Tax Validators
export const createTaxValidator = vine.compile(
  vine.object({
    name: vine.string(),
    value: vine.number(),
    storeId: vine.string().uuid(),
    isDefault: vine.boolean().optional(),
  })
)

export const updateTaxValidator = vine.compile(
  vine.object({
    name: vine.string().optional(),
    value: vine.number().optional(),
    isDefault: vine.boolean().optional(),
  })
)
