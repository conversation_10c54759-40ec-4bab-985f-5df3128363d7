import ZnUser from '#models/zn_user'
import { DateTime } from 'luxon'
import { md5 } from 'js-md5'
import _ from 'lodash'

export class CustomerService {
  async findUserByEmailOrPhone(email: string, phone: string): Promise<ZnUser | null> {
    try {
      if (!email && !phone) return null

      const query = ZnUser.query()

      if (email && phone) {
        query.where('email', email).orWhere('phone', phone)
      } else if (email) {
        query.where('email', email)
      } else if (phone) {
        query.where('phone', phone)
      }

      return await query.first()
    } catch (error) {
      console.error('Error finding user by email or phone:', error)
      throw error
    }
  }

  async associateCustomerWithStore(
    customer: ZnUser,
    storeId: string,
    updateData?: Partial<ZnUser>
  ): Promise<ZnUser> {
    try {
      if (updateData && Object.keys(updateData).length > 0) {
        const userData = { ...updateData, deletedAt: null }
        await customer.merge(userData).save()
      }

      const isAssociated = await customer
        .related('customerStores')
        .query()
        .where('storeId', storeId)
        .first()

      if (!isAssociated) {
        await customer.related('customerStores').attach([storeId])
      }

      await customer.load('customerStores')

      return customer
    } catch (error) {
      console.error('Error associating customer with store:', error)
      throw error
    }
  }

  async createCustomer(
    data: Partial<ZnUser>,
    storeId: string
  ): Promise<{ customer: ZnUser; isNewCustomer: boolean }> {
    try {
      const existingUser = await this.findUserByEmailOrPhone(data.email || '', data.phone || '')

      if (existingUser) {
        const { id, createdAt, updatedAt, ...updateData } = data

        const customer = await this.associateCustomerWithStore(existingUser, storeId, updateData)
        return { customer, isNewCustomer: false }
      } else {
        const userData = { ...data }
        // generate random password
        const timestamp = Date.now().toString()
        const customer = await ZnUser.create({ ...userData, password: md5(timestamp) })
        await customer.related('customerStores').attach([storeId])
        await customer.load('customerStores')

        return { customer, isNewCustomer: true }
      }
    } catch (error) {
      console.error('Error creating customer:', error)
      throw error
    }
  }

  async updateCustomer(customerId: string, data: Partial<ZnUser>): Promise<ZnUser> {
    try {
      const customer = await ZnUser.findOrFail(customerId)
      const userData = { ...data }
      customer.merge(userData)
      await customer.save()

      return customer
    } catch (error) {
      console.error('Error updating customer:', error)
      throw error
    }
  }

  async deleteCustomer(customerId: string): Promise<void> {
    try {
      const customer = await ZnUser.findOrFail(customerId)

      customer.deletedAt = DateTime.now()
      await customer.save()
    } catch (error) {
      console.error('Error deleting customer:', error)
      throw error
    }
  }

  async searchCustomers(
    storeId: string,
    options?: {
      search?: string
      page?: number
      limit?: number
      gender?: string
      sort?: string[] | string
      order?: string
    }
  ) {
    try {
      const { search, page = 1, limit = 10, gender, sort, order } = options || {}

      const query = ZnUser.query()
        .whereHas('customerStores', (query) => {
          query.where('storeId', storeId)
        })
        .preload('avatarMedia')
        .preload('defaultAddress')
        .preload('customerStores')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(email) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(firstName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(lastName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phone) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (gender && gender !== 'all') {
        if (gender === 'none') {
          query.whereNull('gender')
        } else {
          query.where('gender', gender)
        }
      }

      if (sort) {
        const sortArr = Array.isArray(sort) ? sort : [sort]
        if (sortArr.length >= 2) {
          const column = String(sortArr[0])
          const direction = String(sortArr[1]).toLowerCase() === 'asc' ? 'asc' : 'desc'
          const allowedColumns = new Set(['firstName', 'lastName', 'createdAt', 'updatedAt'])
          if (allowedColumns.has(column)) {
            query.orderBy(column, direction as 'asc' | 'desc')
          } else {
            query.orderBy('createdAt', 'desc')
          }
        } else if (order) {
          const column = String(sortArr[0])
          const direction = String(order).toLowerCase() === 'asc' ? 'asc' : 'desc'
          const allowedColumns = new Set(['firstName', 'lastName', 'createdAt', 'updatedAt'])
          if (allowedColumns.has(column)) {
            query.orderBy(column, direction as 'asc' | 'desc')
          } else {
            query.orderBy('createdAt', 'desc')
          }
        } else {
          query.orderBy('createdAt', 'desc')
        }
      } else {
        query.orderBy('createdAt', 'desc')
      }

      return await query.paginate(page, limit)
    } catch (error) {
      console.error('Error searching customers:', error)
      throw error
    }
  }

  // Removed searchCustomersV2: merged into searchCustomers

  async getCustomerDetails(customerId: string): Promise<ZnUser | null> {
    try {
      return await ZnUser.query()
        .where('id', customerId)
        .preload('avatarMedia')
        .preload('defaultAddress')
        .first()
    } catch (error) {
      console.error('Error getting customer details:', error)
      throw error
    }
  }
}
