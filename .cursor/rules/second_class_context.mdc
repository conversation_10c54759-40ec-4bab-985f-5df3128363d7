---
alwaysApply: true
---

# SECOND_CLASS_CONTEXT: BACKEND PROJECT-LEVEL RULES

## 1. Technology Stack Overview

### Core Framework & Runtime

- **AdonisJS v6.14.1** - Primary Node.js/TypeScript framework
- **Node.js** with ES Modules (`"type": "module"`)
- **TypeScript ~5.4** with strict configuration
- **MySQL 8.0** as primary database with read replicas
- **Redis 7.0.11** for caching and session management
- **BullMQ** via @rlanz/bull-queue for job processing

### Key Dependencies

- **Authentication**: @adonisjs/auth with custom JWT guards
- **ORM**: @adonisjs/lucid for database operations
- **Validation**: @vinejs/vine for input validation
- **Queue**: @rlanz/bull-queue for background job processing
- **AWS SDK**: Multiple services (S3, CloudWatch, IVS, Polly, SNS)
- **Shopify**: @shopify/admin-api-client for e-commerce integration
- **AI/ML**: OpenAI API, Pinecone vector database
- **Media**: Sharp for images, FFmpeg for video processing
- **Payments**: PayPal SDK, Authorize.net

### Development Tools

- **ESLint** with AdonisJS plugin and Prettier integration
- **PM2** for production process management
- **Docker Compose** for local development environment

## 2. Architecture & Project Structure

### Architectural Patterns

```
zurno-api/
├── app/                    # Application core
│   ├── controllers/        # HTTP request handlers
│   ├── models/            # Database models (Lucid ORM)
│   ├── services/          # Business logic layer
│   ├── jobs/              # Background job classes
│   ├── middleware/        # HTTP middleware
│   ├── validators/        # Input validation schemas
│   ├── listeners/         # Event listeners
│   └── exceptions/        # Custom exception handlers
├── admin/                 # Admin-specific functionality
├── config/                # Configuration files
├── database/              # Migrations, seeders, SQL
├── start/                 # Application bootstrap
│   └── route/             # Route definitions (v1, v2)
└── services/              # External service integrations
```

### Design Patterns

- **Service Layer Pattern**: Business logic in dedicated service classes
- **Repository Pattern**: Data access through Lucid ORM models
- **Event-Driven Architecture**: Listeners for decoupled operations
- **Job Queue Pattern**: Background processing with BullMQ
- **Soft Delete Pattern**: Data retention with `deletedAt` timestamps
- **Multi-tenant Architecture**: Separate contexts for stores, users, admins

### Database Architecture

- **Primary Database**: MySQL with read/write separation
- **Tracking Database**: Separate MySQL instance for analytics
- **Caching Layer**: Redis for sessions and application cache
- **Queue Storage**: Redis for BullMQ job persistence

## 3. Coding Conventions & Style

### Naming Conventions

- **Variables/Methods**: `camelCase` (e.g., `firstName`, `getUserById`)
- **Classes/Models**: `PascalCase` (e.g., `ZnUser`, `UserService`)
- **Database Tables**: `snake_case` with `zn_` prefix (e.g., `zn_users`, `zn_products`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `PAYMENT_TYPE`, `STREAM_EVENTS`)
- **Files**: `snake_case.ts` for utilities, `PascalCase.ts` for classes

### Import Patterns

```typescript
// Use import aliases for clean imports
import ZnUser from '#models/zn_user'
import { UserService } from '#services/user_service'
import { appAuthSignupValidator } from '#validators/app/auth/signup'
```

### Code Structure

```typescript
// Controller pattern
export default class UserController {
  constructor(
    private userService = new UserService(),
    private awsService = new AmazonS3StorageService()
  ) {}

  async show({ params, response }: HttpContext) {
    try {
      const user = await this.userService.findById(params.id)
      return response.ok(user)
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
```

### Documentation Standards

- **API Endpoints**: JSDoc with Swagger annotations
- **Methods**: Clear docstrings for complex business logic
- **Types**: Comprehensive TypeScript interfaces
- **README**: Maintain up-to-date setup and deployment instructions

## 4. API & Database Design

### API Standards

- **Versioning**: `/v1/`, `/v2/` prefixes for all routes
- **RESTful Design**: Standard HTTP methods and status codes
- **Response Format**: Consistent JSON structure
- **Authentication**: JWT tokens with role-based guards
- **Validation**: VineJS validators for all input
- **Documentation**: Swagger/OpenAPI specifications

### HTTP Status Codes

- `200 OK`: Successful GET, PUT, PATCH
- `201 Created`: Successful POST
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Authorization failed
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server errors

### Database Design Rules

- **Primary Keys**: UUID strings, auto-generated in `beforeCreate` hook
- **Timestamps**: `createdAt`, `updatedAt` on all tables
- **Soft Deletes**: `deletedAt` timestamp, never hard delete
- **Foreign Keys**: Proper constraints with `onDelete` actions
- **Migrations**: All schema changes via migration files
- **Naming**: `zn_` prefix for all tables, descriptive column names

### Model Patterns

```typescript
export default class ZnUser extends AppModel {
  @column({ isPrimary: true })
  declare id: string

  @column()
  declare email: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @hasMany(() => ZnPost)
  declare posts: HasMany<typeof ZnPost>
}
```

## 5. Development Workflow & CI/CD

### Git Workflow

- **Branch Strategy**: Feature branches with descriptive names
- **Commit Messages**: Clear, descriptive commit messages
- **Code Review**: Required for all changes to main branch

### Development Commands

```bash
# Development
yarn dev                    # Start development server with hot reload
yarn queue                  # Start queue workers
yarn lint                   # ESLint code checking
yarn format                 # Prettier code formatting
yarn typecheck              # TypeScript type checking

# Production
yarn build                  # Build for production
yarn start                  # Start production server
```

### Deployment Process

- **Environment Validation**: All required environment variables
- **Database Migrations**: Run migrations before deployment
- **Queue Workers**: Deploy separately from web servers
- **Health Checks**: Verify all services after deployment
- **Rollback Plan**: Maintain ability to rollback quickly

## 6. Quality Standards & Security Practices

### Security Requirements

- **Authentication**: JWT tokens with proper expiration
- **Authorization**: Role-based access control (admin/user guards)
- **Input Validation**: VineJS validators for all user input
- **SQL Injection**: Use ORM queries, never raw SQL with user input
- **CORS**: Proper cross-origin resource sharing configuration
- **Environment Variables**: Secure secrets management
- **Audit Logging**: Track all data modifications

### Performance Standards

- **Database**: Use read replicas for query distribution
- **Caching**: Redis for frequently accessed data
- **Background Jobs**: Queue heavy operations (email, file processing)
- **Image Processing**: Optimize images with Sharp
- **Memory Management**: PM2 restart at 300MB limit
- **Connection Pooling**: Proper database connection management

### Error Handling

```typescript
// Consistent error handling pattern
try {
  const result = await this.service.performOperation(data)
  return response.ok(result)
} catch (error) {
  logger.error('Operation failed', { error, data })
  return response.badRequest({
    message: 'Operation failed',
    details: error.message,
  })
}
```

### Logging Standards

- **Structured Logging**: Use logger service with proper levels
- **Error Tracking**: Log all errors with context
- **Performance Monitoring**: Track slow queries and operations
- **Audit Trail**: Log all data modifications with user context

## 7. Key Architectural Decisions

### Database Strategy

- **Read Replicas**: Separate read/write database connections for performance
- **Soft Deletes**: Maintain data integrity and audit trails with `deletedAt`
- **UUID Primary Keys**: Better security and distributed system support
- **Migration-Driven**: All schema changes through version-controlled migrations
- **Multi-Database**: Separate tracking database for analytics workloads

### Background Processing

- **Queue Architecture**: BullMQ for reliable job processing with Redis
- **Job Types**: Separate queues (default, webhook, syncData, liveStream, etc.)
- **Retry Logic**: Configurable retry attempts (default: 3) with exponential backoff
- **Monitoring**: Track job success/failure rates and performance metrics
- **Concurrency**: Controlled concurrency per queue type for resource management

### Integration Patterns

- **Shopify**: Primary e-commerce backend with admin API client
- **AWS Services**: Cloud infrastructure (S3, CloudWatch, IVS, Polly, SNS)
- **Payment Gateways**: Multiple processor support (PayPal, Authorize.net)
- **AI Services**: OpenAI for language processing, Pinecone for vector search
- **Real-time**: Socket.io for live features (chat, streaming, notifications)

### Scalability Considerations

- **Horizontal Scaling**: Stateless application design with PM2 clustering
- **Database Scaling**: Read replica configuration for query distribution
- **Queue Scaling**: Separate worker processes with independent scaling
- **Caching Strategy**: Redis for session storage and application-level caching
- **CDN Integration**: AWS S3 for static asset delivery and media storage

## 8. Documentation & Maintenance Guidelines

### API Documentation

- **Swagger/OpenAPI**: Auto-generated from JSDoc comments in controllers
- **Endpoint Documentation**: Include request/response examples and validation rules
- **Authentication**: Document required permissions and JWT token usage
- **Error Responses**: Document all possible error scenarios with status codes

### Code Documentation

- **JSDoc Comments**: All public methods and complex business logic
- **Type Definitions**: Comprehensive TypeScript interfaces and types
- **README Files**: Setup, deployment, and troubleshooting guides
- **Architecture Decisions**: Document major design choices and rationale

### Maintenance Practices

- **Dependency Updates**: Regular security and feature updates
- **Database Maintenance**: Regular cleanup of soft-deleted records and optimization
- **Log Rotation**: Prevent log files from consuming excessive disk space
- **Performance Monitoring**: Regular review of slow queries and bottleneck operations
- **Security Audits**: Regular review of dependencies and security practices

### Knowledge Management

- **Memory Bank**: Use allpepper-memory-bank for cross-session knowledge retention
- **Code Reviews**: Share knowledge through comprehensive peer review process
- **Documentation Updates**: Keep documentation current with code changes
- **Onboarding**: Maintain comprehensive developer onboarding guide and setup scripts

### Specific Project Rules (Based on Memory Context)

- **Naming**: Strict adherence to camelCase for models/migrations
- **Backward Compatibility**: Always preserve existing functionality
- **Database Changes**: Use `node ace make:migration` for all schema modifications
- **Affiliate APIs**: Follow existing structure patterns (/v1/affiliates/stats)
- **Authentication**: Optional authentication pattern for engagement endpoints
- **Code Style**: Prettier auto-format on save, keep only essential changes in commits

---

**Note**: These rules complement the first_class_context (ISO-27001, OWASP Top 10) and should be followed for all backend development tasks to ensure consistency, security, and maintainability. All new features must follow these established patterns before deployment.

**Note**: These rules complement the first_class_context (ISO-27001, OWASP Top 10, TDD methodology) and should be followed for all backend development tasks to ensure consistency, security, and maintainability. All new features must follow these established patterns and undergo comprehensive testing before deployment.
